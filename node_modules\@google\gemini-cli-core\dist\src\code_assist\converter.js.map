{"version": 3, "file": "converter.js", "sourceRoot": "", "sources": ["../../../src/code_assist/converter.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAQL,uBAAuB,GAexB,MAAM,eAAe,CAAC;AAmEvB,MAAM,UAAU,mBAAmB,CACjC,GAA0B;IAE1B,OAAO;QACL,OAAO,EAAE;YACP,KAAK,EAAE,SAAS,GAAG,GAAG,CAAC,KAAK;YAC5B,QAAQ,EAAE,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC;SACnC;KACF,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,sBAAsB,CACpC,GAAyB;IAEzB,OAAO;QACL,WAAW,EAAE,GAAG,CAAC,WAAW;KAC7B,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,wBAAwB,CACtC,GAA8B,EAC9B,OAAgB,EAChB,SAAkB;IAElB,OAAO;QACL,KAAK,EAAE,GAAG,CAAC,KAAK;QAChB,OAAO;QACP,OAAO,EAAE,8BAA8B,CAAC,GAAG,EAAE,SAAS,CAAC;KACxD,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,2BAA2B,CACzC,GAA8B;IAE9B,MAAM,KAAK,GAAG,GAAG,CAAC,QAAQ,CAAC;IAC3B,MAAM,GAAG,GAAG,IAAI,uBAAuB,EAAE,CAAC;IAC1C,GAAG,CAAC,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC;IAClC,GAAG,CAAC,+BAA+B,GAAG,KAAK,CAAC,+BAA+B,CAAC;IAC5E,GAAG,CAAC,cAAc,GAAG,KAAK,CAAC,cAAc,CAAC;IAC1C,GAAG,CAAC,aAAa,GAAG,KAAK,CAAC,aAAa,CAAC;IACxC,OAAO,GAAG,CAAC;AACb,CAAC;AAED,SAAS,8BAA8B,CACrC,GAA8B,EAC9B,SAAkB;IAElB,OAAO;QACL,QAAQ,EAAE,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC;QAClC,iBAAiB,EAAE,cAAc,CAAC,GAAG,CAAC,MAAM,EAAE,iBAAiB,CAAC;QAChE,aAAa,EAAE,GAAG,CAAC,MAAM,EAAE,aAAa;QACxC,KAAK,EAAE,GAAG,CAAC,MAAM,EAAE,KAAK;QACxB,UAAU,EAAE,GAAG,CAAC,MAAM,EAAE,UAAU;QAClC,MAAM,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM;QAC1B,cAAc,EAAE,GAAG,CAAC,MAAM,EAAE,cAAc;QAC1C,gBAAgB,EAAE,wBAAwB,CAAC,GAAG,CAAC,MAAM,CAAC;QACtD,UAAU,EAAE,SAAS;KACtB,CAAC;AACJ,CAAC;AAED,SAAS,UAAU,CAAC,QAA0B;IAC5C,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;QAC5B,qCAAqC;QACrC,OAAO,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;IACjC,CAAC;IACD,iCAAiC;IACjC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC;AAC/B,CAAC;AAED,SAAS,cAAc,CAAC,OAAsB;IAC5C,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,OAAO,SAAS,CAAC;IACnB,CAAC;IACD,OAAO,SAAS,CAAC,OAAO,CAAC,CAAC;AAC5B,CAAC;AAED,SAAS,SAAS,CAAC,OAAqB;IACtC,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;QAC3B,sBAAsB;QACtB,OAAO;YACL,IAAI,EAAE,MAAM;YACZ,KAAK,EAAE,OAAO,CAAC,OAAO,CAAC;SACxB,CAAC;IACJ,CAAC;IACD,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;QAChC,gBAAgB;QAChB,OAAO;YACL,IAAI,EAAE,MAAM;YACZ,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;SAC3B,CAAC;IACJ,CAAC;IACD,IAAI,OAAO,IAAI,OAAO,EAAE,CAAC;QACvB,iBAAiB;QACjB,OAAO,OAAO,CAAC;IACjB,CAAC;IACD,cAAc;IACd,OAAO;QACL,IAAI,EAAE,MAAM;QACZ,KAAK,EAAE,CAAC,OAAe,CAAC;KACzB,CAAC;AACJ,CAAC;AAED,SAAS,OAAO,CAAC,KAAkB;IACjC,OAAO,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AAC3B,CAAC;AAED,SAAS,MAAM,CAAC,IAAe;IAC7B,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;QAC7B,gBAAgB;QAChB,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;IACxB,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,wBAAwB,CAC/B,MAA8B;IAE9B,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,OAAO,SAAS,CAAC;IACnB,CAAC;IACD,OAAO;QACL,WAAW,EAAE,MAAM,CAAC,WAAW;QAC/B,IAAI,EAAE,MAAM,CAAC,IAAI;QACjB,IAAI,EAAE,MAAM,CAAC,IAAI;QACjB,cAAc,EAAE,MAAM,CAAC,cAAc;QACrC,eAAe,EAAE,MAAM,CAAC,eAAe;QACvC,aAAa,EAAE,MAAM,CAAC,aAAa;QACnC,gBAAgB,EAAE,MAAM,CAAC,gBAAgB;QACzC,QAAQ,EAAE,MAAM,CAAC,QAAQ;QACzB,eAAe,EAAE,MAAM,CAAC,eAAe;QACvC,gBAAgB,EAAE,MAAM,CAAC,gBAAgB;QACzC,IAAI,EAAE,MAAM,CAAC,IAAI;QACjB,gBAAgB,EAAE,MAAM,CAAC,gBAAgB;QACzC,cAAc,EAAE,MAAM,CAAC,cAAc;QACrC,aAAa,EAAE,MAAM,CAAC,aAAa;QACnC,oBAAoB,EAAE,MAAM,CAAC,oBAAoB;QACjD,kBAAkB,EAAE,MAAM,CAAC,kBAAkB;QAC7C,eAAe,EAAE,MAAM,CAAC,eAAe;QACvC,YAAY,EAAE,MAAM,CAAC,YAAY;QACjC,cAAc,EAAE,MAAM,CAAC,cAAc;QACrC,cAAc,EAAE,MAAM,CAAC,cAAc;KACtC,CAAC;AACJ,CAAC"}