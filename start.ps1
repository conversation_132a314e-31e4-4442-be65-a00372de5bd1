# Gemini CLI OpenAI Proxy Server Launcher
# This script starts a local OpenAI-compatible API server for Gemini
# Use it to integrate with Cursor, Cline, Rucode and other IDEs

param(
    [switch]$UpdateCredentials,  # Update OAuth credentials
    [switch]$Test,              # Run tests after start
    [switch]$Help               # Show help
)

function Write-ColorText {
    param([string]$Text, [string]$Color = "White")
    $Colors = @{
        Success = "Green"
        Warning = "Yellow" 
        Error = "Red"
        Info = "Cyan"
        Header = "Magenta"
    }
    Write-Host $Text -ForegroundColor $Colors[$Color]
}

function Show-Header {
    Clear-Host
    Write-ColorText "================================================================================" "Header"
    Write-ColorText "                    GEMINI CLI OPENAI PROXY SERVER                             " "Header"
    Write-ColorText "                                                                               " "Header"
    Write-ColorText "  Turn Gemini into OpenAI-compatible API for use in any IDE                   " "Header"
    Write-ColorText "================================================================================" "Header"
    Write-Host ""
}

function Show-Help {
    Show-Header
    Write-ColorText "USAGE GUIDE:" "Info"
    Write-Host ""
    Write-ColorText "Launch parameters:" "Info"
    Write-Host "  -UpdateCredentials    Update OAuth credentials from Gemini CLI"
    Write-Host "  -Test                 Run API tests after server start"
    Write-Host "  -Help                 Show this help"
    Write-Host ""
    Write-ColorText "Examples:" "Info"
    Write-Host "  .\start.ps1                    # Normal start"
    Write-Host "  .\start.ps1 -UpdateCredentials # Update tokens and start"
    Write-Host "  .\start.ps1 -Test              # Start with tests"
    Write-Host ""
    Write-ColorText "IDE Configuration:" "Info"
    Write-Host "  Base URL:    http://127.0.0.1:8787/v1"
    Write-Host "  API Key:     sk-gemini-proxy-12345"
    Write-Host "  Models:      gemini-2.5-pro, gemini-2.5-flash"
    Write-Host ""
    exit 0
}

function Test-Prerequisites {
    Write-ColorText "Checking prerequisites..." "Info"
    
    # Check Node.js
    try {
        $nodeVersion = node --version 2>$null
        Write-ColorText "Node.js: $nodeVersion" "Success"
    } catch {
        Write-ColorText "Node.js not found! Install Node.js from https://nodejs.org" "Error"
        exit 1
    }
    
    # Check npm
    try {
        $npmVersion = npm --version 2>$null
        Write-ColorText "npm: v$npmVersion" "Success"
    } catch {
        Write-ColorText "npm not found!" "Error"
        exit 1
    }
    
    # Check Gemini CLI
    try {
        $geminiVersion = gemini --version 2>$null
        Write-ColorText "Gemini CLI installed" "Success"
    } catch {
        Write-ColorText "Gemini CLI not found. Installing..." "Warning"
        npm install -g @google/gemini-cli
        Write-ColorText "Gemini CLI installed" "Success"
    }
    
    # Check project files
    if (-not (Test-Path "package.json")) {
        Write-ColorText "package.json not found! Run script from project folder." "Error"
        exit 1
    }
    
    if (-not (Test-Path "src/index.ts")) {
        Write-ColorText "Source files not found! Check project structure." "Error"
        exit 1
    }
    
    Write-ColorText "All prerequisites met" "Success"
    Write-Host ""
}

function Update-OAuthCredentials {
    Write-ColorText "Updating OAuth credentials..." "Info"
    
    $credentialsPath = "$env:USERPROFILE\.gemini\oauth_creds.json"
    
    if (-not (Test-Path $credentialsPath)) {
        Write-ColorText "OAuth credentials not found!" "Error"
        Write-ColorText "Run 'gemini' and authenticate with Google" "Warning"
        Write-Host ""
        Write-ColorText "Starting Gemini CLI for authentication..." "Info"
        Start-Process "gemini" -Wait
        
        if (-not (Test-Path $credentialsPath)) {
            Write-ColorText "Authentication not completed!" "Error"
            exit 1
        }
    }
    
    try {
        $credentials = Get-Content $credentialsPath | ConvertFrom-Json | ConvertTo-Json -Compress
        
        # Read current .dev.vars
        $devVarsContent = Get-Content ".dev.vars" -Raw
        
        # Update GCP_SERVICE_ACCOUNT
        $devVarsContent = $devVarsContent -replace 'GCP_SERVICE_ACCOUNT=.*', "GCP_SERVICE_ACCOUNT=$credentials"
        
        # Write back
        $devVarsContent | Set-Content ".dev.vars" -NoNewline
        
        Write-ColorText "OAuth credentials updated" "Success"
    } catch {
        Write-ColorText "Error updating credentials: $($_.Exception.Message)" "Error"
        exit 1
    }
    
    Write-Host ""
}

function Install-Dependencies {
    Write-ColorText "Checking dependencies..." "Info"
    
    if (-not (Test-Path "node_modules")) {
        Write-ColorText "Installing dependencies..." "Info"
        npm install
        if ($LASTEXITCODE -ne 0) {
            Write-ColorText "Error installing dependencies!" "Error"
            exit 1
        }
        Write-ColorText "Dependencies installed" "Success"
    } else {
        Write-ColorText "Dependencies already installed" "Success"
    }
    Write-Host ""
}

function Show-Configuration {
    Write-ColorText "SERVER CONFIGURATION:" "Info"
    Write-Host ""
    Write-ColorText "Local URL:      http://127.0.0.1:8787" "Success"
    Write-ColorText "API Base URL:   http://127.0.0.1:8787/v1" "Success"
    Write-ColorText "API Key:        sk-gemini-proxy-12345" "Success"
    Write-Host ""
    Write-ColorText "Available models:" "Info"
    Write-Host "   • gemini-2.5-pro    (for complex tasks)"
    Write-Host "   • gemini-2.5-flash  (for quick responses)"
    Write-Host ""
    Write-ColorText "IDE SETUP:" "Info"
    Write-Host ""
    Write-ColorText "Cursor:" "Info"
    Write-Host "   Settings -> Models -> Add Model"
    Write-Host "   Provider: OpenAI"
    Write-Host "   Base URL: http://127.0.0.1:8787/v1"
    Write-Host "   API Key: sk-gemini-proxy-12345"
    Write-Host ""
    Write-ColorText "Cline (VS Code):" "Info"
    Write-Host "   API Provider: OpenAI"
    Write-Host "   Base URL: http://127.0.0.1:8787/v1"
    Write-Host "   API Key: sk-gemini-proxy-12345"
    Write-Host ""
    Write-ColorText "Rucode:" "Info"
    Write-Host "   Settings -> AI -> Custom OpenAI"
    Write-Host "   Endpoint: http://127.0.0.1:8787/v1"
    Write-Host "   API Key: sk-gemini-proxy-12345"
    Write-Host ""
    Write-ColorText "IMPORTANT: Server must be running for API to work!" "Warning"
    Write-Host ""
}

function Start-Server {
    Write-ColorText "Starting Gemini OpenAI Proxy Server..." "Info"
    Write-Host ""
    
    # Show startup info
    Write-ColorText "Server will be available at: http://127.0.0.1:8787" "Success"
    Write-ColorText "API endpoint: http://127.0.0.1:8787/v1" "Success"
    Write-ColorText "API Key: sk-gemini-proxy-12345" "Success"
    Write-Host ""
    Write-ColorText "Startup may take a few seconds..." "Info"
    Write-Host ""
    Write-ColorText "Press Ctrl+C to stop the server" "Warning"
    Write-Host ""
    Write-ColorText "================================================================================" "Header"
    Write-Host ""
    
    # Start server
    try {
        npm run dev
    } catch {
        Write-ColorText "Server startup error: $($_.Exception.Message)" "Error"
        exit 1
    }
}

function Test-API {
    Write-ColorText "Running API tests..." "Info"
    Write-Host ""
    
    # Wait a bit for server to start
    Start-Sleep -Seconds 3
    
    try {
        node test-proxy.js
        Write-ColorText "Tests completed" "Success"
    } catch {
        Write-ColorText "Testing error: $($_.Exception.Message)" "Error"
    }
    Write-Host ""
}

function Show-QuickStart {
    Write-Host ""
    Write-ColorText "QUICK START:" "Header"
    Write-Host ""
    Write-ColorText "1. Copy these settings to your IDE:" "Info"
    Write-Host ""
    Write-Host "   Base URL: " -NoNewline
    Write-ColorText "http://127.0.0.1:8787/v1" "Success"
    Write-Host "   API Key:  " -NoNewline  
    Write-ColorText "sk-gemini-proxy-12345" "Success"
    Write-Host ""
    Write-ColorText "2. Choose model:" "Info"
    Write-Host "   • gemini-2.5-pro (slower but smarter)"
    Write-Host "   • gemini-2.5-flash (faster)"
    Write-Host ""
    Write-ColorText "3. Start using Gemini through OpenAI API!" "Success"
    Write-Host ""
}

# ============================================================================
# MAIN SCRIPT LOGIC
# ============================================================================

# Show help if requested
if ($Help) {
    Show-Help
}

# Show header
Show-Header

# Check prerequisites
Test-Prerequisites

# Update credentials if requested
if ($UpdateCredentials) {
    Update-OAuthCredentials
}

# Install dependencies
Install-Dependencies

# Show configuration
Show-Configuration

# Show quick start
Show-QuickStart

# Run tests in background if requested
if ($Test) {
    Write-ColorText "Tests will run after server starts..." "Info"
    Write-Host ""
    
    # Start tests in background after a few seconds
    Start-Job -ScriptBlock {
        Start-Sleep -Seconds 5
        Set-Location $using:PWD
        node test-proxy.js
    } | Out-Null
}

# Start server (blocking call)
Start-Server
