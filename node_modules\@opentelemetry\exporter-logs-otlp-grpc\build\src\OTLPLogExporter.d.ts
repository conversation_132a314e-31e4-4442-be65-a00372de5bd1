import { LogRecordExporter, ReadableLogRecord } from '@opentelemetry/sdk-logs';
import { OTLPGRPCExporterConfigNode, OTLPGRPCExporterNodeBase } from '@opentelemetry/otlp-grpc-exporter-base';
import { IExportLogsServiceResponse } from '@opentelemetry/otlp-transformer';
/**
 * OTLP Logs Exporter for Node
 */
export declare class OTLPLogExporter extends OTLPGRPCExporterNodeBase<ReadableLogRecord, IExportLogsServiceResponse> implements LogRecordExporter {
    constructor(config?: OTLPGRPCExporterConfigNode);
    getDefaultUrl(config: OTLPGRPCExporterConfigNode): string;
    getUrlFromConfig(config: OTLPGRPCExporterConfigNode): string;
}
//# sourceMappingURL=OTLPLogExporter.d.ts.map