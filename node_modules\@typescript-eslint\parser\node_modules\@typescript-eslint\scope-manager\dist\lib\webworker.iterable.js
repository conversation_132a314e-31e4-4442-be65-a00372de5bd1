"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib repo
Object.defineProperty(exports, "__esModule", { value: true });
exports.webworker_iterable = void 0;
const base_config_1 = require("./base-config");
exports.webworker_iterable = {
    libs: [],
    variables: [
        ['CSSNumericArray', base_config_1.TYPE],
        ['CSSTransformValue', base_config_1.TYPE],
        ['CSSUnparsedValue', base_config_1.TYPE],
        ['Cache', base_config_1.TYPE],
        ['CanvasPath', base_config_1.TYPE],
        ['CanvasPathDrawingStyles', base_config_1.TYPE],
        ['DOMStringList', base_config_1.TYPE],
        ['FileList', base_config_1.TYPE],
        ['FontFaceSet', base_config_1.TYPE],
        ['FormDataIterator', base_config_1.TYPE],
        ['FormData', base_config_1.TYPE],
        ['HeadersIterator', base_config_1.TYPE],
        ['Headers', base_config_1.TYPE],
        ['IDBDatabase', base_config_1.TYPE],
        ['IDBObjectStore', base_config_1.TYPE],
        ['ImageTrackList', base_config_1.TYPE],
        ['MessageEvent', base_config_1.TYPE],
        ['StylePropertyMapReadOnlyIterator', base_config_1.TYPE],
        ['StylePropertyMapReadOnly', base_config_1.TYPE],
        ['SubtleCrypto', base_config_1.TYPE],
        ['URLSearchParamsIterator', base_config_1.TYPE],
        ['URLSearchParams', base_config_1.TYPE],
        ['WEBGL_draw_buffers', base_config_1.TYPE],
        ['WEBGL_multi_draw', base_config_1.TYPE],
        ['WebGL2RenderingContextBase', base_config_1.TYPE],
        ['WebGL2RenderingContextOverloads', base_config_1.TYPE],
        ['WebGLRenderingContextBase', base_config_1.TYPE],
        ['WebGLRenderingContextOverloads', base_config_1.TYPE],
    ],
};
