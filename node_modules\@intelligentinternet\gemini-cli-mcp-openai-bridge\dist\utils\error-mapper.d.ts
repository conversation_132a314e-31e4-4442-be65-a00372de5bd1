import { type OpenAIErrorResponse } from '../types.js';
/**
 * Maps a caught error from the Gemini API or auth flow to a standard
 * OpenAI error object and a corresponding HTTP status code.
 * @param error The caught unknown error.
 * @returns An object containing the standard OpenAI error and a suggested status code.
 */
export declare function mapErrorToOpenAIError(error: unknown): {
    openAIError: OpenAIErrorResponse;
    statusCode: number;
};
