#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

// Конфигурация по умолчанию
const DEFAULT_CONFIG = {
  host: '127.0.0.1',
  port: 8765,
  mode: 'configured',
  debug: true
};

// Парсинг аргументов командной строки
function parseArgs() {
  const args = process.argv.slice(2);
  const config = { ...DEFAULT_CONFIG };
  
  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    
    if (arg === '--help' || arg === '-h') {
      showHelp();
      process.exit(0);
    } else if (arg.startsWith('--host=')) {
      config.host = arg.split('=')[1];
    } else if (arg.startsWith('--port=')) {
      config.port = parseInt(arg.split('=')[1]);
    } else if (arg.startsWith('--mode=')) {
      config.mode = arg.split('=')[1];
    } else if (arg === '--no-debug') {
      config.debug = false;
    }
  }
  
  return config;
}

function showHelp() {
  console.log(`
CLI Proxy - Gemini CLI MCP OpenAI Bridge Wrapper

Использование:
  node index.js [опции]
  npm start [-- опции]

Опции:
  --host=HOST     Хост для прослушивания (по умолчанию: 127.0.0.1)
  --port=PORT     Порт для прослушивания (по умолчанию: 8765)
  --mode=MODE     Режим безопасности: read-only, edit, configured, yolo
                  (по умолчанию: configured)
  --no-debug      Отключить отладочный вывод
  --help, -h      Показать эту справку

Примеры:
  node index.js                           # Запуск с настройками по умолчанию
  node index.js --port=9000               # Запуск на порту 9000
  node index.js --mode=read-only          # Безопасный режим только для чтения
  
Доступные npm скрипты:
  npm run proxy:safe    # Режим только для чтения
  npm run proxy:edit    # Режим редактирования файлов
  npm run proxy:config  # Настроенный режим (рекомендуется)

После запуска сервер будет доступен по адресам:
  - OpenAI API: http://localhost:8765/v1/chat/completions
  - MCP endpoint: http://localhost:8765/mcp
  - Models: http://localhost:8765/v1/models
`);
}

function startProxy(config) {
  console.log('🚀 Запуск CLI Proxy...');
  console.log(`📍 Конфигурация:`, config);
  
  // Формируем аргументы для gemini-cli-bridge
  const bridgeArgs = [
    `--host=${config.host}`,
    `--port=${config.port}`,
    `--mode=${config.mode}`
  ];
  
  if (config.debug) {
    bridgeArgs.push('--debug');
  }
  
  // Запускаем bridge напрямую
  const bridgePath = path.join(__dirname, 'node_modules', '@intelligentinternet', 'gemini-cli-mcp-openai-bridge', 'dist', 'index.js');
  const bridge = spawn('node', [bridgePath, ...bridgeArgs], {
    stdio: 'inherit',
    cwd: process.cwd()
  });
  
  bridge.on('error', (error) => {
    console.error('❌ Ошибка запуска bridge:', error.message);
    process.exit(1);
  });
  
  bridge.on('close', (code) => {
    console.log(`🔚 Bridge завершился с кодом ${code}`);
    process.exit(code);
  });
  
  // Обработка сигналов завершения
  process.on('SIGINT', () => {
    console.log('\n🛑 Получен сигнал завершения, останавливаем proxy...');
    bridge.kill('SIGINT');
  });
  
  process.on('SIGTERM', () => {
    console.log('\n🛑 Получен сигнал завершения, останавливаем proxy...');
    bridge.kill('SIGTERM');
  });
}

// Основная функция
function main() {
  const config = parseArgs();
  startProxy(config);
}

// Запуск только если файл выполняется напрямую
if (require.main === module) {
  main();
}

module.exports = { parseArgs, startProxy, showHelp };
