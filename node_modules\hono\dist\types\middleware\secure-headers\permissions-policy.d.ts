export type PermissionsPolicyDirective = StandardizedFeatures | ProposedFeatures | ExperimentalFeatures;
/**
 * These features have been declared in a published version of the respective specification.
 */
type StandardizedFeatures = "accelerometer" | "ambientLightSensor" | "attributionReporting" | "autoplay" | "battery" | "bluetooth" | "camera" | "chUa" | "chUaArch" | "chUaBitness" | "chUaFullVersion" | "chUaFullVersionList" | "chUaMobile" | "chUaModel" | "chUaPlatform" | "chUaPlatformVersion" | "chUaWow64" | "computePressure" | "crossOriginIsolated" | "directSockets" | "displayCapture" | "encryptedMedia" | "executionWhileNotRendered" | "executionWhileOutOfViewport" | "fullscreen" | "geolocation" | "gyroscope" | "hid" | "identityCredentialsGet" | "idleDetection" | "keyboardMap" | "magnetometer" | "microphone" | "midi" | "navigationOverride" | "payment" | "pictureInPicture" | "publickeyCredentialsGet" | "screenWakeLock" | "serial" | "storageAccess" | "syncXhr" | "usb" | "webShare" | "windowManagement" | "xrSpatialTracking";
/**
 * These features have been proposed, but the definitions have not yet been integrated into their respective specs.
 */
type ProposedFeatures = "clipboardRead" | "clipboardWrite" | "gemepad" | "sharedAutofill" | "speakerSelection";
/**
 * These features generally have an explainer only, but may be available for experimentation by web developers.
 */
type ExperimentalFeatures = "allScreensCapture" | "browsingTopics" | "capturedSurfaceControl" | "conversionMeasurement" | "digitalCredentialsGet" | "focusWithoutUserActivation" | "joinAdInterestGroup" | "localFonts" | "runAdAuction" | "smartCard" | "syncScript" | "trustTokenRedemption" | "unload" | "verticalScroll";
export {};
