/**
 * @module
 * MIME utility.
 */
export declare const getMimeType: (filename: string, mimes?: Record<string, string>) => string | undefined;
export declare const getExtension: (mimeType: string) => string | undefined;
export { baseMimes as mimes };
/**
 * Union types for BaseMime
 */
export type BaseMime = (typeof _baseMimes)[keyof typeof _baseMimes];
declare const _baseMimes: {
    readonly aac: "audio/aac";
    readonly avi: "video/x-msvideo";
    readonly avif: "image/avif";
    readonly av1: "video/av1";
    readonly bin: "application/octet-stream";
    readonly bmp: "image/bmp";
    readonly css: "text/css";
    readonly csv: "text/csv";
    readonly eot: "application/vnd.ms-fontobject";
    readonly epub: "application/epub+zip";
    readonly gif: "image/gif";
    readonly gz: "application/gzip";
    readonly htm: "text/html";
    readonly html: "text/html";
    readonly ico: "image/x-icon";
    readonly ics: "text/calendar";
    readonly jpeg: "image/jpeg";
    readonly jpg: "image/jpeg";
    readonly js: "text/javascript";
    readonly json: "application/json";
    readonly jsonld: "application/ld+json";
    readonly map: "application/json";
    readonly mid: "audio/x-midi";
    readonly midi: "audio/x-midi";
    readonly mjs: "text/javascript";
    readonly mp3: "audio/mpeg";
    readonly mp4: "video/mp4";
    readonly mpeg: "video/mpeg";
    readonly oga: "audio/ogg";
    readonly ogv: "video/ogg";
    readonly ogx: "application/ogg";
    readonly opus: "audio/opus";
    readonly otf: "font/otf";
    readonly pdf: "application/pdf";
    readonly png: "image/png";
    readonly rtf: "application/rtf";
    readonly svg: "image/svg+xml";
    readonly tif: "image/tiff";
    readonly tiff: "image/tiff";
    readonly ts: "video/mp2t";
    readonly ttf: "font/ttf";
    readonly txt: "text/plain";
    readonly wasm: "application/wasm";
    readonly webm: "video/webm";
    readonly weba: "audio/webm";
    readonly webmanifest: "application/manifest+json";
    readonly webp: "image/webp";
    readonly woff: "font/woff";
    readonly woff2: "font/woff2";
    readonly xhtml: "application/xhtml+xml";
    readonly xml: "application/xml";
    readonly zip: "application/zip";
    readonly "3gp": "video/3gpp";
    readonly "3g2": "video/3gpp2";
    readonly gltf: "model/gltf+json";
    readonly glb: "model/gltf-binary";
};
declare const baseMimes: Record<string, BaseMime>;
