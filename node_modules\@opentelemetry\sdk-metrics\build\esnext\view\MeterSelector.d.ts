import { Predicate } from './Predicate';
export interface MeterSelectorCriteria {
    name?: string;
    version?: string;
    schemaUrl?: string;
}
export declare class MeterSelector {
    private _nameFilter;
    private _versionFilter;
    private _schemaUrlFilter;
    constructor(criteria?: MeterSelectorCriteria);
    getNameFilter(): Predicate;
    /**
     * TODO: semver filter? no spec yet.
     */
    getVersionFilter(): Predicate;
    getSchemaUrlFilter(): Predicate;
}
//# sourceMappingURL=MeterSelector.d.ts.map