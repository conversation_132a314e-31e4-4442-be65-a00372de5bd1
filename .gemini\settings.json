{"securityPolicy": {"mode": "configured", "allowedTools": ["read_file", "list_directory", "glob", "google_web_search", "web_fetch", "write_file", "replace"], "shellCommandPolicy": {"allow": ["ls -l", "dir", "git status", "git log --oneline -10", "npm run test", "npm list", "node --version", "npm --version"], "deny": ["rm", "del", "sudo", "docker", "format", "shutdown", "reboot"]}}, "usageStatisticsEnabled": false, "telemetry": {"enabled": false}}