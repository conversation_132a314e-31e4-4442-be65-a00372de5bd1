export declare const DEFAULT_PROJECT_FILES_ERROR_EXPLANATION = "\n\nHaving many files run with the default project is known to cause performance issues and slow down linting.\n\nSee https://typescript-eslint.io/troubleshooting/typed-linting#allowdefaultproject-glob-too-wide\n";
export declare function validateDefaultProjectForFilesGlob(allowDefaultProject: string[] | undefined): void;
//# sourceMappingURL=validateDefaultProjectForFilesGlob.d.ts.map