{"version": 3, "file": "contentGenerator.js", "sourceRoot": "", "sources": ["../../../src/core/contentGenerator.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAOL,WAAW,GACZ,MAAM,eAAe,CAAC;AACvB,OAAO,EAAE,gCAAgC,EAAE,MAAM,8BAA8B,CAAC;AAChF,OAAO,EAAE,oBAAoB,EAAE,MAAM,qBAAqB,CAAC;AAE3D,OAAO,EAAE,iBAAiB,EAAE,MAAM,iBAAiB,CAAC;AAsBpD,MAAM,CAAN,IAAY,QAKX;AALD,WAAY,QAAQ;IAClB,gDAAoC,CAAA;IACpC,yCAA6B,CAAA;IAC7B,uCAA2B,CAAA;IAC3B,uCAA2B,CAAA;AAC7B,CAAC,EALW,QAAQ,KAAR,QAAQ,QAKnB;AAUD,MAAM,UAAU,4BAA4B,CAC1C,MAAc,EACd,QAA8B;IAE9B,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,SAAS,CAAC;IAC7D,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,SAAS,CAAC;IAC7D,MAAM,kBAAkB,GAAG,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,SAAS,CAAC;IACzE,MAAM,mBAAmB,GAAG,OAAO,CAAC,GAAG,CAAC,qBAAqB,IAAI,SAAS,CAAC;IAE3E,yFAAyF;IACzF,MAAM,cAAc,GAAG,MAAM,CAAC,QAAQ,EAAE,IAAI,oBAAoB,CAAC;IAEjE,MAAM,sBAAsB,GAA2B;QACrD,KAAK,EAAE,cAAc;QACrB,QAAQ;QACR,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE;KAC1B,CAAC;IAEF,kGAAkG;IAClG,IACE,QAAQ,KAAK,QAAQ,CAAC,iBAAiB;QACvC,QAAQ,KAAK,QAAQ,CAAC,WAAW,EACjC,CAAC;QACD,OAAO,sBAAsB,CAAC;IAChC,CAAC;IAED,IAAI,QAAQ,KAAK,QAAQ,CAAC,UAAU,IAAI,YAAY,EAAE,CAAC;QACrD,sBAAsB,CAAC,MAAM,GAAG,YAAY,CAAC;QAC7C,sBAAsB,CAAC,QAAQ,GAAG,KAAK,CAAC;QACxC,iBAAiB,CACf,sBAAsB,CAAC,MAAM,EAC7B,sBAAsB,CAAC,KAAK,EAC5B,sBAAsB,CAAC,KAAK,CAC7B,CAAC;QAEF,OAAO,sBAAsB,CAAC;IAChC,CAAC;IAED,IACE,QAAQ,KAAK,QAAQ,CAAC,aAAa;QACnC,CAAC,YAAY,IAAI,CAAC,kBAAkB,IAAI,mBAAmB,CAAC,CAAC,EAC7D,CAAC;QACD,sBAAsB,CAAC,MAAM,GAAG,YAAY,CAAC;QAC7C,sBAAsB,CAAC,QAAQ,GAAG,IAAI,CAAC;QAEvC,OAAO,sBAAsB,CAAC;IAChC,CAAC;IAED,OAAO,sBAAsB,CAAC;AAChC,CAAC;AAED,MAAM,CAAC,KAAK,UAAU,sBAAsB,CAC1C,MAA8B,EAC9B,QAAgB,EAChB,SAAkB;IAElB,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,OAAO,CAAC,OAAO,CAAC;IAC3D,MAAM,WAAW,GAAG;QAClB,OAAO,EAAE;YACP,YAAY,EAAE,aAAa,OAAO,KAAK,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,IAAI,GAAG;SAC5E;KACF,CAAC;IACF,IACE,MAAM,CAAC,QAAQ,KAAK,QAAQ,CAAC,iBAAiB;QAC9C,MAAM,CAAC,QAAQ,KAAK,QAAQ,CAAC,WAAW,EACxC,CAAC;QACD,OAAO,gCAAgC,CACrC,WAAW,EACX,MAAM,CAAC,QAAQ,EACf,QAAQ,EACR,SAAS,CACV,CAAC;IACJ,CAAC;IAED,IACE,MAAM,CAAC,QAAQ,KAAK,QAAQ,CAAC,UAAU;QACvC,MAAM,CAAC,QAAQ,KAAK,QAAQ,CAAC,aAAa,EAC1C,CAAC;QACD,MAAM,WAAW,GAAG,IAAI,WAAW,CAAC;YAClC,MAAM,EAAE,MAAM,CAAC,MAAM,KAAK,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM;YACxD,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,WAAW;SACZ,CAAC,CAAC;QAEH,OAAO,WAAW,CAAC,MAAM,CAAC;IAC5B,CAAC;IAED,MAAM,IAAI,KAAK,CACb,0DAA0D,MAAM,CAAC,QAAQ,EAAE,CAC5E,CAAC;AACJ,CAAC"}