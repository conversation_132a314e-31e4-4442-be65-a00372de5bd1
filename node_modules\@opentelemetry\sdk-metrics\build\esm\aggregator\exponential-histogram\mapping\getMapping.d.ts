import { Mapping } from './types';
/**
 * getMapping returns an appropriate mapping for the given scale. For scales -10
 * to 0 the underlying type will be ExponentMapping. For scales 1 to 20 the
 * underlying type will be LogarithmMapping.
 * @param scale a number in the range [-10, 20]
 * @returns {Mapping}
 */
export declare function getMapping(scale: number): Mapping;
//# sourceMappingURL=getMapping.d.ts.map