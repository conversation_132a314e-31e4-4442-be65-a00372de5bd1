{"version": 3, "file": "converter.test.js", "sourceRoot": "", "sources": ["../../../src/code_assist/converter.test.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE,MAAM,EAAE,MAAM,QAAQ,CAAC;AAC9C,OAAO,EACL,wBAAwB,EACxB,2BAA2B,GAE5B,MAAM,gBAAgB,CAAC;AACxB,OAAO,EAEL,uBAAuB,EACvB,YAAY,EACZ,aAAa,GACd,MAAM,eAAe,CAAC;AAEvB,QAAQ,CAAC,WAAW,EAAE,GAAG,EAAE;IACzB,QAAQ,CAAC,qBAAqB,EAAE,GAAG,EAAE;QACnC,EAAE,CAAC,8CAA8C,EAAE,GAAG,EAAE;YACtD,MAAM,QAAQ,GAA8B;gBAC1C,KAAK,EAAE,YAAY;gBACnB,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;aACzD,CAAC;YACF,MAAM,aAAa,GAAG,wBAAwB,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;YACvE,MAAM,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC;gBAC5B,KAAK,EAAE,YAAY;gBACnB,OAAO,EAAE,YAAY;gBACrB,OAAO,EAAE;oBACP,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;oBACxD,iBAAiB,EAAE,SAAS;oBAC5B,aAAa,EAAE,SAAS;oBACxB,KAAK,EAAE,SAAS;oBAChB,UAAU,EAAE,SAAS;oBACrB,MAAM,EAAE,SAAS;oBACjB,cAAc,EAAE,SAAS;oBACzB,gBAAgB,EAAE,SAAS;oBAC3B,UAAU,EAAE,SAAS;iBACtB;aACF,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,QAAQ,GAA8B;gBAC1C,KAAK,EAAE,YAAY;gBACnB,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;aACzD,CAAC;YACF,MAAM,aAAa,GAAG,wBAAwB,CAAC,QAAQ,CAAC,CAAC;YACzD,MAAM,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC;gBAC5B,KAAK,EAAE,YAAY;gBACnB,OAAO,EAAE,SAAS;gBAClB,OAAO,EAAE;oBACP,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;oBACxD,iBAAiB,EAAE,SAAS;oBAC5B,aAAa,EAAE,SAAS;oBACxB,KAAK,EAAE,SAAS;oBAChB,UAAU,EAAE,SAAS;oBACrB,MAAM,EAAE,SAAS;oBACjB,cAAc,EAAE,SAAS;oBACzB,gBAAgB,EAAE,SAAS;oBAC3B,UAAU,EAAE,SAAS;iBACtB;aACF,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;YACjD,MAAM,QAAQ,GAA8B;gBAC1C,KAAK,EAAE,YAAY;gBACnB,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;aACzD,CAAC;YACF,MAAM,aAAa,GAAG,wBAAwB,CAC5C,QAAQ,EACR,YAAY,EACZ,aAAa,CACd,CAAC;YACF,MAAM,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC;gBAC5B,KAAK,EAAE,YAAY;gBACnB,OAAO,EAAE,YAAY;gBACrB,OAAO,EAAE;oBACP,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;oBACxD,iBAAiB,EAAE,SAAS;oBAC5B,aAAa,EAAE,SAAS;oBACxB,KAAK,EAAE,SAAS;oBAChB,UAAU,EAAE,SAAS;oBACrB,MAAM,EAAE,SAAS;oBACjB,cAAc,EAAE,SAAS;oBACzB,gBAAgB,EAAE,SAAS;oBAC3B,UAAU,EAAE,aAAa;iBAC1B;aACF,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,QAAQ,GAA8B;gBAC1C,KAAK,EAAE,YAAY;gBACnB,QAAQ,EAAE,OAAO;aAClB,CAAC;YACF,MAAM,aAAa,GAAG,wBAAwB,CAAC,QAAQ,CAAC,CAAC;YACzD,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC;gBAC7C,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,EAAE;aAC7C,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACtC,MAAM,QAAQ,GAA8B;gBAC1C,KAAK,EAAE,YAAY;gBACnB,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC;aACjD,CAAC;YACF,MAAM,aAAa,GAAG,wBAAwB,CAAC,QAAQ,CAAC,CAAC;YACzD,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC;gBAC7C,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,EAAE;gBAC5C,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,EAAE;aAC7C,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,GAAG,EAAE;YAC3C,MAAM,QAAQ,GAA8B;gBAC1C,KAAK,EAAE,YAAY;gBACnB,QAAQ,EAAE,OAAO;gBACjB,MAAM,EAAE;oBACN,iBAAiB,EAAE,8BAA8B;iBAClD;aACF,CAAC;YACF,MAAM,aAAa,GAAG,wBAAwB,CAAC,QAAQ,CAAC,CAAC;YACzD,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC;gBACtD,IAAI,EAAE,MAAM;gBACZ,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,8BAA8B,EAAE,CAAC;aAClD,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;YACzC,MAAM,QAAQ,GAA8B;gBAC1C,KAAK,EAAE,YAAY;gBACnB,QAAQ,EAAE,OAAO;gBACjB,MAAM,EAAE;oBACN,WAAW,EAAE,GAAG;oBAChB,IAAI,EAAE,EAAE;iBACT;aACF,CAAC;YACF,MAAM,aAAa,GAAG,wBAAwB,CAAC,QAAQ,CAAC,CAAC;YACzD,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC;gBACrD,WAAW,EAAE,GAAG;gBAChB,IAAI,EAAE,EAAE;aACT,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE,GAAG,EAAE;YACpD,MAAM,QAAQ,GAA8B;gBAC1C,KAAK,EAAE,YAAY;gBACnB,QAAQ,EAAE,OAAO;gBACjB,MAAM,EAAE;oBACN,WAAW,EAAE,GAAG;oBAChB,IAAI,EAAE,GAAG;oBACT,IAAI,EAAE,CAAC;oBACP,cAAc,EAAE,CAAC;oBACjB,eAAe,EAAE,CAAC;oBAClB,aAAa,EAAE,CAAC,GAAG,CAAC;oBACpB,gBAAgB,EAAE,IAAI;oBACtB,QAAQ,EAAE,CAAC;oBACX,eAAe,EAAE,GAAG;oBACpB,gBAAgB,EAAE,GAAG;oBACrB,IAAI,EAAE,CAAC;oBACP,gBAAgB,EAAE,kBAAkB;iBACrC;aACF,CAAC;YACF,MAAM,aAAa,GAAG,wBAAwB,CAAC,QAAQ,CAAC,CAAC;YACzD,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC;gBACrD,WAAW,EAAE,GAAG;gBAChB,IAAI,EAAE,GAAG;gBACT,IAAI,EAAE,CAAC;gBACP,cAAc,EAAE,CAAC;gBACjB,eAAe,EAAE,CAAC;gBAClB,aAAa,EAAE,CAAC,GAAG,CAAC;gBACpB,gBAAgB,EAAE,IAAI;gBACtB,QAAQ,EAAE,CAAC;gBACX,eAAe,EAAE,GAAG;gBACpB,gBAAgB,EAAE,GAAG;gBACrB,IAAI,EAAE,CAAC;gBACP,gBAAgB,EAAE,kBAAkB;aACrC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACtC,EAAE,CAAC,kCAAkC,EAAE,GAAG,EAAE;YAC1C,MAAM,aAAa,GAA8B;gBAC/C,QAAQ,EAAE;oBACR,UAAU,EAAE;wBACV;4BACE,KAAK,EAAE,CAAC;4BACR,OAAO,EAAE;gCACP,IAAI,EAAE,OAAO;gCACb,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC;6BAC/B;4BACD,YAAY,EAAE,YAAY,CAAC,IAAI;4BAC/B,aAAa,EAAE,EAAE;yBAClB;qBACF;iBACF;aACF,CAAC;YACF,MAAM,QAAQ,GAAG,2BAA2B,CAAC,aAAa,CAAC,CAAC;YAC5D,MAAM,CAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,uBAAuB,CAAC,CAAC;YACzD,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QACzE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,aAAa,GAA8B;gBAC/C,QAAQ,EAAE;oBACR,UAAU,EAAE,EAAE;oBACd,cAAc,EAAE;wBACd,WAAW,EAAE,aAAa,CAAC,MAAM;wBACjC,aAAa,EAAE,EAAE;qBAClB;oBACD,aAAa,EAAE;wBACb,gBAAgB,EAAE,EAAE;wBACpB,oBAAoB,EAAE,EAAE;wBACxB,eAAe,EAAE,EAAE;qBACpB;iBACF;aACF,CAAC;YACF,MAAM,QAAQ,GAAG,2BAA2B,CAAC,aAAa,CAAC,CAAC;YAC5D,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,OAAO,CACrC,aAAa,CAAC,QAAQ,CAAC,cAAc,CACtC,CAAC;YACF,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,OAAO,CACpC,aAAa,CAAC,QAAQ,CAAC,aAAa,CACrC,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;YAC1D,MAAM,aAAa,GAA8B;gBAC/C,QAAQ,EAAE;oBACR,UAAU,EAAE,EAAE;oBACd,+BAA+B,EAAE;wBAC/B;4BACE,IAAI,EAAE,OAAO;4BACb,KAAK,EAAE;gCACL;oCACE,YAAY,EAAE;wCACZ,IAAI,EAAE,eAAe;wCACrB,IAAI,EAAE;4CACJ,GAAG,EAAE,KAAK;yCACX;qCACF;iCACF;6BACF;yBACF;qBACF;iBACF;aACF,CAAC;YACF,MAAM,QAAQ,GAAG,2BAA2B,CAAC,aAAa,CAAC,CAAC;YAC5D,MAAM,CAAC,QAAQ,CAAC,+BAA+B,CAAC,CAAC,OAAO,CACtD,aAAa,CAAC,QAAQ,CAAC,+BAA+B,CACvD,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}