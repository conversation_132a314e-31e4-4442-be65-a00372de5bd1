const LOG_PREFIX = '[BRIDGE-SERVER]';
// eslint-disable-next-line @typescript-eslint/no-explicit-any
function log(level, ...args) {
    const timestamp = new Date().toISOString();
    // Select the appropriate console method based on the log level.
    const logFunction = console[level.toLowerCase()] ||
        console.log;
    // Optimize printing of error objects for clarity.
    const finalArgs = args.map(arg => {
        if (arg instanceof Error) {
            return { message: arg.message, stack: arg.stack };
        }
        return arg;
    });
    logFunction(`${timestamp} ${LOG_PREFIX} [${level}]`, ...finalArgs);
}
export const logger = {
    info: (...args) => log('INFO', ...args),
    warn: (...args) => log('WARN', ...args),
    error: (...args) => log('ERROR', ...args),
    // The debug method only logs if debugMode is true.
    debug: (debugMode, ...args) => {
        if (debugMode) {
            log('DEBUG', ...args);
        }
    },
};
//# sourceMappingURL=logger.js.map