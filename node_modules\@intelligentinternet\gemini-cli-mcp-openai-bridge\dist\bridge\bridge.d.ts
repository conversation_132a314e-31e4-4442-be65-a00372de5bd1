import { Application } from 'express';
import { type Config, type Tool as GcliTool } from '@google/gemini-cli-core';
import { type SecurityPolicy } from '../types.js';
export declare class GcliMcpBridge {
    private readonly config;
    private readonly cliVersion;
    private readonly securityPolicy;
    private readonly debugMode;
    private readonly resolveRedirects;
    private readonly sessions;
    constructor(config: Config, cliVersion: string, securityPolicy: SecurityPolicy, debugMode?: boolean, resolveRedirects?: boolean);
    getAvailableTools(): Promise<GcliTool[]>;
    private createNewMcpServer;
    start(app: Application): Promise<void>;
    private registerAllGcliTools;
    private resolveRedirectUrl;
    private isReadOnlyTool;
    private isShellCommandAllowed;
    private registerGcliTool;
    private convertJsonSchemaToZod;
    private convertGcliResultToMcpResult;
}
