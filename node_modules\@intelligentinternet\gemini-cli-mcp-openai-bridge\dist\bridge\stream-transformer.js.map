{"version": 3, "file": "stream-transformer.js", "sourceRoot": "", "sources": ["../../src/bridge/stream-transformer.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,aAAa,CAAC;AA8BzC,mCAAmC;AACnC,MAAM,UAAU,6BAA6B,CAC3C,KAAa,EACb,SAAS,GAAG,KAAK;IAEjB,MAAM,MAAM,GAAG,YAAY,UAAU,EAAE,EAAE,CAAC;IAC1C,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;IACnD,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;IAClC,IAAI,YAAY,GAAG,IAAI,CAAC;IACxB,IAAI,aAAa,GAAG,CAAC,CAAC;IAEtB,MAAM,WAAW,GAAG,CAClB,KAAkB,EAClB,gBAA+B,IAAI,EACtB,EAAE,CAAC,CAAC;QACjB,EAAE,EAAE,MAAM;QACV,MAAM,EAAE,uBAAuB;QAC/B,OAAO,EAAE,YAAY;QACrB,KAAK,EAAE,KAAK;QACZ,OAAO,EAAE;YACP;gBACE,KAAK,EAAE,CAAC;gBACR,KAAK;gBACL,aAAa;aACd;SACF;KACF,CAAC,CAAC;IAEH,MAAM,YAAY,GAAG,CACnB,UAAwD,EACxD,KAAkB,EAClB,EAAE;QACF,MAAM,SAAS,GAAG,SAAS,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC;QACvD,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC;IAChD,CAAC,CAAC;IAEF,OAAO,IAAI,eAAe,CAAC;QACzB,SAAS,CAAC,KAAkB,EAAE,UAAU;YACtC,IAAI,SAAS,EAAE,CAAC;gBACd,OAAO,CAAC,GAAG,CACT,wCAAwC,KAAK,CAAC,IAAI,EAAE,EACpD,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAC7C,CAAC;YACJ,CAAC;YACD,IAAI,KAAK,GAAgB,EAAE,CAAC;YAE5B,IAAI,YAAY,EAAE,CAAC;gBACjB,KAAK,CAAC,IAAI,GAAG,WAAW,CAAC;gBACzB,YAAY,GAAG,KAAK,CAAC;YACvB,CAAC;YAED,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;gBACnB,KAAK,MAAM;oBACT,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;wBACf,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC;wBAC3B,YAAY,CAAC,UAAU,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC;oBAC/C,CAAC;oBACD,MAAM;gBAER,KAAK,WAAW,CAAC,CAAC,CAAC;oBACjB,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC;oBAClC,qGAAqG;oBACrG,MAAM,UAAU,GAAG,QAAQ,IAAI,IAAI,UAAU,EAAE,EAAE,CAAC;oBAElD,yDAAyD;oBACzD,kDAAkD;oBAClD,MAAM,SAAS,GAAgB;wBAC7B,GAAG,KAAK,EAAE,uCAAuC;wBACjD,UAAU,EAAE;4BACV;gCACE,KAAK,EAAE,aAAa;gCACpB,EAAE,EAAE,UAAU;gCACd,IAAI,EAAE,UAAU;gCAChB,QAAQ,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,EAAE;6BACxC;yBACF;qBACF,CAAC;oBACF,YAAY,CAAC,UAAU,EAAE,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC;oBAEjD,8CAA8C;oBAC9C,MAAM,SAAS,GAAgB;wBAC7B,UAAU,EAAE;4BACV;gCACE,KAAK,EAAE,aAAa;gCACpB,EAAE,EAAE,UAAU;gCACd,IAAI,EAAE,UAAU;gCAChB,QAAQ,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;6BAC9C;yBACF;qBACF,CAAC;oBACF,YAAY,CAAC,UAAU,EAAE,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC;oBAEjD,aAAa,EAAE,CAAC;oBAChB,MAAM;gBACR,CAAC;gBAED,KAAK,WAAW;oBACd,sGAAsG;oBACtG,IAAI,SAAS,EAAE,CAAC;wBACd,OAAO,CAAC,GAAG,CAAC,wCAAwC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;oBACpE,CAAC;oBACD,MAAM;YACV,CAAC;QACH,CAAC;QAED,KAAK,CAAC,UAAU;YACd,4EAA4E;YAC5E,MAAM,aAAa,GAAG,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC;YAChE,YAAY,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,EAAE,aAAa,CAAC,CAAC,CAAC;YAEzD,MAAM,UAAU,GAAG,kBAAkB,CAAC;YACtC,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;QACjD,CAAC;KACF,CAAC,CAAC;AACL,CAAC"}