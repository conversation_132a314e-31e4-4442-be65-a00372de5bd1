{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../../../../src/platform/browser/util.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAC1C,OAAO,EAEL,gBAAgB,EAChB,kBAAkB,GACnB,MAAM,qBAAqB,CAAC;AAG7B;;;;;GAKG;AACH,MAAM,UAAU,WAAW,CACzB,MAAc,EACd,OAAgC;IAEhC,IAAI,UAAkC,CAAC;IACvC,IAAM,SAAS,GAAG,OAAO,SAAS,CAAC,UAAU,KAAK,UAAU,IAAI,CAAC,OAAO,CAAC;IACzE,IAAI,OAAO,EAAE;QACX,UAAU,cACR,MAAM,EAAE,kBAAkB,EAC1B,cAAc,EAAE,kBAAkB,IAC/B,OAAO,CACX,CAAC;KACH;IAED;;OAEG;IACH,OAAO,SAAS,IAAI,CAClB,WAA+B,EAC/B,IAAoC;QAEpC,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;YAC5B,IAAI,CAAC,KAAK,CAAC,8BAA8B,CAAC,CAAC;YAC3C,OAAO,IAAI,CAAC,EAAE,IAAI,EAAE,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC;SACjD;QACD,IAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QAC5C,IAAI,SAAS,EAAE;YACb,cAAc,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;SACvC;aAAM;YACL,WAAW,CAAC,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;SAChD;IACH,CAAC,CAAC;AACJ,CAAC;AAED;;;;;GAKG;AACH,SAAS,cAAc,CACrB,IAAY,EACZ,IAAoC,EACpC,MAAc;IAEd,IAAI,SAAS,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE;QACtC,IAAI,CAAC,KAAK,CAAC,uBAAuB,EAAE,IAAI,CAAC,CAAC;QAC1C,IAAI,CAAC,EAAE,IAAI,EAAE,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC;KAC1C;SAAM;QACL,IAAI,CAAC;YACH,IAAI,EAAE,gBAAgB,CAAC,MAAM;YAC7B,KAAK,EAAE,IAAI,KAAK,CAAC,8BAA4B,IAAM,CAAC;SACrD,CAAC,CAAC;KACJ;AACH,CAAC;AAED;;;;;;GAMG;AACH,SAAS,WAAW,CAClB,IAAY,EACZ,IAAoC,EACpC,MAAc,EACd,UAAuC;IAAvC,2BAAA,EAAA,eAAuC;IAEvC,IAAM,GAAG,GAAG,IAAI,cAAc,EAAE,CAAC;IACjC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IACzB,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,UAAC,EAAM;YAAN,KAAA,aAAM,EAAL,CAAC,QAAA,EAAE,CAAC,QAAA;QACvC,GAAG,CAAC,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC;IAEH,GAAG,CAAC,kBAAkB,GAAG;QACvB,IAAI,GAAG,CAAC,UAAU,KAAK,cAAc,CAAC,IAAI,EAAE;YAC1C,IAAM,UAAU,GAAG,GAAG,CAAC,MAAM,IAAI,CAAC,CAAC;YACnC,IAAI,CAAC,KAAK,CAAC,kCAAgC,UAAU,gBAAW,IAAM,CAAC,CAAC;YAExE,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,IAAI,GAAG,CAAC,MAAM,GAAG,GAAG,EAAE;gBACzC,OAAO,IAAI,CAAC,EAAE,IAAI,EAAE,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC;aACjD;iBAAM;gBACL,OAAO,IAAI,CAAC;oBACV,IAAI,EAAE,gBAAgB,CAAC,MAAM;oBAC7B,KAAK,EAAE,IAAI,KAAK,CACd,6CAA2C,GAAG,CAAC,MAAQ,CACxD;iBACF,CAAC,CAAC;aACJ;SACF;IACH,CAAC,CAAC;IAEF,GAAG,CAAC,OAAO,GAAG,UAAA,GAAG;QACf,kBAAkB,CAAC,IAAI,KAAK,CAAC,2BAAyB,GAAK,CAAC,CAAC,CAAC;QAC9D,OAAO,IAAI,CAAC,EAAE,IAAI,EAAE,gBAAgB,CAAC,MAAM,EAAE,CAAC,CAAC;IACjD,CAAC,CAAC;IAEF,kCAAkC;IAClC,IAAI,CAAC,KAAK,CAAC,6BAA2B,IAAM,CAAC,CAAC;IAC9C,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACjB,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { diag } from '@opentelemetry/api';\nimport {\n  ExportResult,\n  ExportResultCode,\n  globalErrorHandler,\n} from '@opentelemetry/core';\nimport * as zipkinTypes from '../../types';\n\n/**\n * Prepares send function that will send spans to the remote Zipkin service.\n * @param urlStr - url to send spans\n * @param headers - headers\n * send\n */\nexport function prepareSend(\n  urlStr: string,\n  headers?: Record<string, string>\n): zipkinTypes.SendFn {\n  let xhrHeaders: Record<string, string>;\n  const useBeacon = typeof navigator.sendBeacon === 'function' && !headers;\n  if (headers) {\n    xhrHeaders = {\n      Accept: 'application/json',\n      'Content-Type': 'application/json',\n      ...headers,\n    };\n  }\n\n  /**\n   * Send spans to the remote Zipkin service.\n   */\n  return function send(\n    zipkinSpans: zipkinTypes.Span[],\n    done: (result: ExportResult) => void\n  ) {\n    if (zipkinSpans.length === 0) {\n      diag.debug('Zipkin send with empty spans');\n      return done({ code: ExportResultCode.SUCCESS });\n    }\n    const payload = JSON.stringify(zipkinSpans);\n    if (useBeacon) {\n      sendWithBeacon(payload, done, urlStr);\n    } else {\n      sendWithXhr(payload, done, urlStr, xhrHeaders);\n    }\n  };\n}\n\n/**\n * Sends data using beacon\n * @param data\n * @param done\n * @param urlStr\n */\nfunction sendWithBeacon(\n  data: string,\n  done: (result: ExportResult) => void,\n  urlStr: string\n) {\n  if (navigator.sendBeacon(urlStr, data)) {\n    diag.debug('sendBeacon - can send', data);\n    done({ code: ExportResultCode.SUCCESS });\n  } else {\n    done({\n      code: ExportResultCode.FAILED,\n      error: new Error(`sendBeacon - cannot send ${data}`),\n    });\n  }\n}\n\n/**\n * Sends data using XMLHttpRequest\n * @param data\n * @param done\n * @param urlStr\n * @param xhrHeaders\n */\nfunction sendWithXhr(\n  data: string,\n  done: (result: ExportResult) => void,\n  urlStr: string,\n  xhrHeaders: Record<string, string> = {}\n) {\n  const xhr = new XMLHttpRequest();\n  xhr.open('POST', urlStr);\n  Object.entries(xhrHeaders).forEach(([k, v]) => {\n    xhr.setRequestHeader(k, v);\n  });\n\n  xhr.onreadystatechange = () => {\n    if (xhr.readyState === XMLHttpRequest.DONE) {\n      const statusCode = xhr.status || 0;\n      diag.debug(`Zipkin response status code: ${statusCode}, body: ${data}`);\n\n      if (xhr.status >= 200 && xhr.status < 400) {\n        return done({ code: ExportResultCode.SUCCESS });\n      } else {\n        return done({\n          code: ExportResultCode.FAILED,\n          error: new Error(\n            `Got unexpected status code from zipkin: ${xhr.status}`\n          ),\n        });\n      }\n    }\n  };\n\n  xhr.onerror = msg => {\n    globalErrorHandler(new Error(`Zipkin request error: ${msg}`));\n    return done({ code: ExportResultCode.FAILED });\n  };\n\n  // Issue request to remote service\n  diag.debug(`Zipkin request payload: ${data}`);\n  xhr.send(data);\n}\n"]}