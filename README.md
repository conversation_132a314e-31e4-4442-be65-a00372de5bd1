# CLI Proxy

Простой CLI proxy сервер на основе Gemini CLI MCP OpenAI Bridge, который предоставляет OpenAI-совместимый API для работы с моделями Gemini.

## Быстрый старт

### 1. Установка зависимостей
```bash
npm install
```

### 2. Запуск сервера

#### Простой запуск
```bash
npm start
```

#### Различные режимы безопасности
```bash
# Безопасный режим (только чтение)
npm run proxy:safe

# Режим редактирования файлов
npm run proxy:edit

# Настроенный режим (рекомендуется)
npm run proxy:config
```

#### Ручной запуск с параметрами
```bash
node index.js --port=9000 --mode=edit
```

## Конфигурация

### Режимы безопасности

- **`read-only`** - Только чтение файлов и веб-поиск
- **`edit`** - Добавляет возможность редактирования файлов
- **`configured`** - Точный контроль через `.gemini/settings.json`
- **`yolo`** - Все возможности (опасно!)

### Настройка в `.gemini/settings.json`

Файл уже создан с безопасными настройками. Вы можете изменить:

- `allowedTools` - список разрешенных инструментов
- `shellCommandPolicy` - политика выполнения команд
- `usageStatisticsEnabled` - отправка статистики

## API Endpoints

После запуска сервер предоставляет следующие endpoints:

### OpenAI-совместимый API
- **Chat Completions**: `http://localhost:8765/v1/chat/completions`
- **Models**: `http://localhost:8765/v1/models`

### MCP Protocol
- **MCP Endpoint**: `http://localhost:8765/mcp`

## Примеры использования

### Тестирование API через curl

```bash
# Получить список моделей
curl http://localhost:8765/v1/models

# Отправить сообщение
curl -X POST http://localhost:8765/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer dummy-key" \
  -d '{
    "model": "gemini-2.5-flash",
    "messages": [
      {"role": "user", "content": "Привет! Как дела?"}
    ]
  }'
```

### Интеграция с другими инструментами

Используйте `http://localhost:8765/v1` как базовый URL для любых инструментов, поддерживающих OpenAI API:

- **Open WebUI**
- **Langchain**
- **LlamaIndex**
- **И многие другие**

## Аутентификация

Сервер использует те же учетные данные, что и `gemini-cli`. Убедитесь, что у вас настроена аутентификация для Gemini API.

## Безопасность

⚠️ **Важно**: Сервер по умолчанию слушает только на `127.0.0.1` и использует безопасные настройки. Не изменяйте хост на `0.0.0.0` без дополнительных мер безопасности.

## Устранение неполадок

### Ошибки аутентификации
Убедитесь, что `gemini-cli` правильно настроен и аутентифицирован.

### Порт уже используется
Измените порт с помощью параметра `--port=XXXX`.

### Отладка
Используйте флаг `--debug` для подробного логирования.

## Лицензия

Apache 2.0
