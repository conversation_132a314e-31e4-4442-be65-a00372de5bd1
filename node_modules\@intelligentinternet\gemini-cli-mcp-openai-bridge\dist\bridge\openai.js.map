{"version": 3, "file": "openai.js", "sourceRoot": "", "sources": ["../../src/bridge/openai.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAqB,MAAM,SAAS,CAAC;AAEpD,OAAO,EAAE,6BAA6B,EAAE,MAAM,yBAAyB,CAAC;AACxE,OAAO,EAAE,eAAe,EAAE,MAAM,qBAAqB,CAAC;AAQtD,OAAO,EAAE,qBAAqB,EAAE,MAAM,0BAA0B,CAAC;AACjE,OAAO,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AAC5C,OAAO,EAAE,UAAU,EAAE,MAAM,aAAa,CAAC;AAEzC,MAAM,UAAU,kBAAkB,CAAC,MAAc,EAAE,SAAS,GAAG,KAAK;IAClE,MAAM,MAAM,GAAG,MAAM,EAAE,CAAC;IAExB,+CAA+C;IAC/C,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE;QAC3B,GAAW,CAAC,SAAS,GAAG,UAAU,EAAE,CAAC;QACtC,IAAI,EAAE,CAAC;IACT,CAAC,CAAC,CAAC;IAEH,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;QACrE,MAAM,SAAS,GAAI,GAAW,CAAC,SAAS,CAAC;QACzC,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,GAAG,CAAC,IAAmC,CAAC;YAErD,MAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE;gBAC5C,SAAS;gBACT,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,MAAM,EAAE,IAAI,CAAC,MAAM;aACpB,CAAC,CAAC;YACH,MAAM,CAAC,KAAK,CAAC,SAAS,EAAE,eAAe,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;YAC9D,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC;YAErC,MAAM,MAAM,GAAG,IAAI,eAAe,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;YAEtD,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,iBAAiB,CAAC;gBAClD,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,WAAW,EAAE,IAAI,CAAC,WAAW;aAC9B,CAAC,CAAC;YAEH,IAAI,MAAM,EAAE,CAAC;gBACX,6BAA6B;gBAC7B,GAAG,CAAC,SAAS,CAAC,cAAc,EAAE,mBAAmB,CAAC,CAAC;gBACnD,GAAG,CAAC,SAAS,CAAC,eAAe,EAAE,UAAU,CAAC,CAAC;gBAC3C,GAAG,CAAC,SAAS,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;gBAC1C,GAAG,CAAC,YAAY,EAAE,CAAC;gBAEnB,MAAM,YAAY,GAAG,6BAA6B,CAChD,IAAI,CAAC,KAAK,EACV,SAAS,CACV,CAAC;gBAEF,+BAA+B;gBAC/B,2DAA2D;gBAC3D,MAAM,cAAc,GAAG,IAAI,cAAc,CAAC;oBACxC,KAAK,CAAC,KAAK,CAAC,UAAU;wBACpB,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,YAAY,EAAE,CAAC;4BACvC,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBAC5B,CAAC;wBACD,UAAU,CAAC,KAAK,EAAE,CAAC;oBACrB,CAAC;iBACF,CAAC,CAAC;gBAEH,2CAA2C;gBAC3C,MAAM,iBAAiB,GAAG,cAAc,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;gBACnE,MAAM,MAAM,GAAG,iBAAiB,CAAC,SAAS,EAAE,CAAC;gBAE7C,iFAAiF;gBACjF,IAAI,CAAC;oBACH,OAAO,IAAI,EAAE,CAAC;wBACZ,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;wBAC5C,IAAI,IAAI,EAAE,CAAC;4BACT,MAAM;wBACR,CAAC;wBACD,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;oBACnB,CAAC;gBACH,CAAC;wBAAS,CAAC;oBACT,MAAM,CAAC,WAAW,EAAE,CAAC;gBACvB,CAAC;gBACD,sCAAsC;gBAEtC,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBAC1C,MAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE;oBAC5C,SAAS;oBACT,MAAM,EAAE,SAAS;oBACjB,UAAU;iBACX,CAAC,CAAC;gBACH,GAAG,CAAC,GAAG,EAAE,CAAC;YACZ,CAAC;iBAAM,CAAC;gBACN,8BAA8B;gBAE9B,IAAI,eAAe,GAAG,EAAE,CAAC;gBACzB,MAAM,SAAS,GAAqB,EAAE,CAAC;gBACvC,IAAI,YAAY,GAAgD,MAAM,CAAC;gBAEvE,gDAAgD;gBAChD,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,YAAY,EAAE,CAAC;oBACvC,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;wBACxC,eAAe,IAAI,KAAK,CAAC,IAAI,CAAC;oBAChC,CAAC;yBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,WAAW,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;wBACpD,MAAM,UAAU,GAAG,QAAQ,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,UAAU,EAAE,EAAE,CAAC;wBAC7D,SAAS,CAAC,IAAI,CAAC;4BACb,EAAE,EAAE,UAAU;4BACd,IAAI,EAAE,UAAU;4BAChB,QAAQ,EAAE;gCACR,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI;gCACrB,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;6BAC3C;yBACF,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;gBAED,yDAAyD;gBACzD,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACzB,YAAY,GAAG,YAAY,CAAC;gBAC9B,CAAC;gBAED,mDAAmD;gBACnD,MAAM,gBAAgB,GAAgC;oBACpD,IAAI,EAAE,WAAW;oBACjB,OAAO,EAAE,eAAe,IAAI,IAAI,EAAE,iDAAiD;iBACpF,CAAC;gBAEF,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACzB,gBAAgB,CAAC,UAAU,GAAG,SAAS,CAAC;gBAC1C,CAAC;gBAED,MAAM,aAAa,GAAyB;oBAC1C,EAAE,EAAE,YAAY,UAAU,EAAE,EAAE;oBAC9B,MAAM,EAAE,iBAAiB;oBACzB,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC;oBACrC,KAAK,EAAE,IAAI,CAAC,KAAK;oBACjB,OAAO,EAAE;wBACP;4BACE,KAAK,EAAE,CAAC;4BACR,OAAO,EAAE,gBAAgB;4BACzB,aAAa,EAAE,YAAY;yBAC5B;qBACF;oBACD,yEAAyE;iBAC1E,CAAC;gBAEF,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBAC1C,MAAM,CAAC,IAAI,CAAC,8CAA8C,EAAE;oBAC1D,SAAS;oBACT,MAAM,EAAE,SAAS;oBACjB,UAAU;iBACX,CAAC,CAAC;gBAEH,4BAA4B;gBAC5B,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACtC,CAAC;QACH,CAAC;QAAC,OAAO,CAAU,EAAE,CAAC;YACpB,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC1C,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,CAAU,EAAE;gBACvD,SAAS;gBACT,UAAU;aACX,CAAC,CAAC;YAEH,aAAa;YACb,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,GAAG,qBAAqB,CAAC,CAAC,CAAC,CAAC;YAE7D,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC;gBACrB,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC3C,CAAC;iBAAM,CAAC;gBACN,gEAAgE;gBAChE,0CAA0C;gBAC1C,GAAG,CAAC,KAAK,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,EAAE,KAAK,EAAE,WAAW,CAAC,KAAK,EAAE,CAAC,MAAM,CAAC,CAAC;gBACvE,GAAG,CAAC,GAAG,EAAE,CAAC;YACZ,CAAC;QACH,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,6CAA6C;IAC7C,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;QACjC,sEAAsE;QACtE,GAAG,CAAC,IAAI,CAAC;YACP,MAAM,EAAE,MAAM;YACd,IAAI,EAAE;gBACJ,EAAE,EAAE,EAAE,gBAAgB,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE;gBAC7D,EAAE,EAAE,EAAE,kBAAkB,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE;aAChE;SACF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,OAAO,MAAM,CAAC;AAChB,CAAC"}