# Gemini CLI OpenAI Worker Environment Variables

# Required: OAuth2 credentials <PERSON><PERSON><PERSON> from Gemini CLI authentication
# Получите актуальные credentials командой: Get-Content "$env:USERPROFILE\.gemini\oauth_creds.json" | ConvertFrom-Json | ConvertTo-<PERSON><PERSON> -Compress
GCP_SERVICE_ACCOUNT={"access_token":"*************************************************************************************************************************************************************************************************************************************","scope":"openid https://www.googleapis.com/auth/userinfo.email https://www.googleapis.com/auth/cloud-platform https://www.googleapis.com/auth/userinfo.profile","token_type":"Bearer","id_token":"eyJhbGciOiJSUzI1NiIsImtpZCI6ImRkNTMwMTIwNGZjMWQ2YTBkNjhjNzgzYTM1Y2M5YzEwYjI1ZTFmNGEiLCJ0eXAiOiJKV1QifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.GUTSosMJtyY1e65p2Sn-l3u-jQgFeKDyP1NpOmA1-E2LX3_Nt2cdUg-oVoahHlkDlkQ6ONvxrj3KqhFS5O_AlZ78vxXfhZnfMV_yUlwOb5omyOlQrSss8cfmYBnrKkEPxUjYLuGQLNVtXfawNVdaKDn8UXSwhoo1nPt0FLIoABAtwzYyR8_k9tr2prGweQYVG6Rz8oHjv0vjoTXlbsKX_ZDTa_ToNol7k4LRGaW8nJp-IjuwxPG64ZCCuX49eEsuJCKRH7RbYvEAObwahjNwQ4r260J3RKQH6uAhE6F1T38G6dKxlqlgUENc00RCzRFV4CkGdfqmMt2NKKFR8Kh5gg","expiry_date":1753873859487,"refresh_token":"1//0fhWzYvwJ_2bdCgYIARAAGA8SNwF-L9Ir7QWLL1VsQfYP8KERzfltZlJhwoIyfPW6JFiWZyq5SN6NdX_BmTdMxUteF-9dwq5TvQ0"}

# Required: Google Cloud Project ID
GEMINI_PROJECT_ID=ragpull

# Optional: API key for authentication (if not set, API is public)
OPENAI_API_KEY=sk-gemini-proxy-12345

# Optional: Enable real Gemini thinking output
ENABLE_REAL_THINKING=true

# Optional: Stream thinking as content with <thinking> tags (DeepSeek R1 style)
STREAM_THINKING_AS_CONTENT=true

# Optional: Auto switch from Pro to flash when you are getting rate-limited
ENABLE_AUTO_MODEL_SWITCHING=true
