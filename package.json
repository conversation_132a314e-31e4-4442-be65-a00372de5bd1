{"name": "cli-proxy", "version": "1.0.0", "description": "CLI Proxy using Gemini CLI MCP OpenAI Bridge", "main": "index.js", "scripts": {"start": "node index.js", "test": "node test-api.js", "proxy": "npx @intelligentinternet/gemini-cli-mcp-openai-bridge", "proxy:safe": "npx @intelligentinternet/gemini-cli-mcp-openai-bridge --mode=read-only --port=8765 --debug", "proxy:edit": "npx @intelligentinternet/gemini-cli-mcp-openai-bridge --mode=edit --port=8765 --debug", "proxy:config": "npx @intelligentinternet/gemini-cli-mcp-openai-bridge --mode=configured --port=8765 --debug"}, "dependencies": {"@intelligentinternet/gemini-cli-mcp-openai-bridge": "^0.1.13"}}