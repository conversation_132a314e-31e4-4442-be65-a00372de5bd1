{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../src/baggage/types.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { baggageEntryMetadataSymbol } from './internal/symbol';\n\n/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport interface BaggageEntry {\n  /** `String` value of the `BaggageEntry`. */\n  value: string;\n  /**\n   * Metadata is an optional string property defined by the W3C baggage specification.\n   * It currently has no special meaning defined by the specification.\n   */\n  metadata?: BaggageEntryMetadata;\n}\n\n/**\n * Serializable Metadata defined by the W3C baggage specification.\n * It currently has no special meaning defined by the OpenTelemetry or W3C.\n */\nexport type BaggageEntryMetadata = { toString(): string } & {\n  __TYPE__: typeof baggageEntryMetadataSymbol;\n};\n\n/**\n * Baggage represents collection of key-value pairs with optional metadata.\n * Each key of Baggage is associated with exactly one value.\n * Baggage may be used to annotate and enrich telemetry data.\n */\nexport interface Baggage {\n  /**\n   * Get an entry from Baggage if it exists\n   *\n   * @param key The key which identifies the BaggageEntry\n   */\n  getEntry(key: string): BaggageEntry | undefined;\n\n  /**\n   * Get a list of all entries in the Baggage\n   */\n  getAllEntries(): [string, BaggageEntry][];\n\n  /**\n   * Returns a new baggage with the entries from the current bag and the specified entry\n   *\n   * @param key string which identifies the baggage entry\n   * @param entry BaggageEntry for the given key\n   */\n  setEntry(key: string, entry: BaggageEntry): Baggage;\n\n  /**\n   * Returns a new baggage with the entries from the current bag except the removed entry\n   *\n   * @param key key identifying the entry to be removed\n   */\n  removeEntry(key: string): Baggage;\n\n  /**\n   * Returns a new baggage with the entries from the current bag except the removed entries\n   *\n   * @param key keys identifying the entries to be removed\n   */\n  removeEntries(...key: string[]): Baggage;\n\n  /**\n   * Returns a new baggage with no entries\n   */\n  clear(): Baggage;\n}\n"]}