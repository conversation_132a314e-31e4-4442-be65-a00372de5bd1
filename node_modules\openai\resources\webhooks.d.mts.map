{"version": 3, "file": "webhooks.d.mts", "sourceRoot": "", "sources": ["../src/resources/webhooks.ts"], "names": [], "mappings": "OAGO,EAAE,WAAW,EAAE;OACf,EAAgB,WAAW,EAAE;AAEpC,qBAAa,QAAS,SAAQ,WAAW;;IACvC;;OAEG;IACG,MAAM,CACV,OAAO,EAAE,MAAM,EACf,OAAO,EAAE,WAAW,EACpB,MAAM,GAAE,MAAM,GAAG,SAAS,GAAG,IAAiC,EAC9D,SAAS,GAAE,MAAY,GACtB,OAAO,CAAC,kBAAkB,CAAC;IAM9B;;;;;;;;;OASG;IACG,eAAe,CACnB,OAAO,EAAE,MAAM,EACf,OAAO,EAAE,WAAW,EACpB,MAAM,GAAE,MAAM,GAAG,SAAS,GAAG,IAAiC,EAC9D,SAAS,GAAE,MAAY,GACtB,OAAO,CAAC,IAAI,CAAC;CAuGjB;AAED;;GAEG;AACH,MAAM,WAAW,0BAA0B;IACzC;;OAEG;IACH,EAAE,EAAE,MAAM,CAAC;IAEX;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,IAAI,EAAE,0BAA0B,CAAC,IAAI,CAAC;IAEtC;;OAEG;IACH,IAAI,EAAE,iBAAiB,CAAC;IAExB;;OAEG;IACH,MAAM,CAAC,EAAE,OAAO,CAAC;CAClB;AAED,yBAAiB,0BAA0B,CAAC;IAC1C;;OAEG;IACH,UAAiB,IAAI;QACnB;;WAEG;QACH,EAAE,EAAE,MAAM,CAAC;KACZ;CACF;AAED;;GAEG;AACH,MAAM,WAAW,0BAA0B;IACzC;;OAEG;IACH,EAAE,EAAE,MAAM,CAAC;IAEX;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,IAAI,EAAE,0BAA0B,CAAC,IAAI,CAAC;IAEtC;;OAEG;IACH,IAAI,EAAE,iBAAiB,CAAC;IAExB;;OAEG;IACH,MAAM,CAAC,EAAE,OAAO,CAAC;CAClB;AAED,yBAAiB,0BAA0B,CAAC;IAC1C;;OAEG;IACH,UAAiB,IAAI;QACnB;;WAEG;QACH,EAAE,EAAE,MAAM,CAAC;KACZ;CACF;AAED;;GAEG;AACH,MAAM,WAAW,wBAAwB;IACvC;;OAEG;IACH,EAAE,EAAE,MAAM,CAAC;IAEX;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,IAAI,EAAE,wBAAwB,CAAC,IAAI,CAAC;IAEpC;;OAEG;IACH,IAAI,EAAE,eAAe,CAAC;IAEtB;;OAEG;IACH,MAAM,CAAC,EAAE,OAAO,CAAC;CAClB;AAED,yBAAiB,wBAAwB,CAAC;IACxC;;OAEG;IACH,UAAiB,IAAI;QACnB;;WAEG;QACH,EAAE,EAAE,MAAM,CAAC;KACZ;CACF;AAED;;GAEG;AACH,MAAM,WAAW,uBAAuB;IACtC;;OAEG;IACH,EAAE,EAAE,MAAM,CAAC;IAEX;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,IAAI,EAAE,uBAAuB,CAAC,IAAI,CAAC;IAEnC;;OAEG;IACH,IAAI,EAAE,cAAc,CAAC;IAErB;;OAEG;IACH,MAAM,CAAC,EAAE,OAAO,CAAC;CAClB;AAED,yBAAiB,uBAAuB,CAAC;IACvC;;OAEG;IACH,UAAiB,IAAI;QACnB;;WAEG;QACH,EAAE,EAAE,MAAM,CAAC;KACZ;CACF;AAED;;GAEG;AACH,MAAM,WAAW,2BAA2B;IAC1C;;OAEG;IACH,EAAE,EAAE,MAAM,CAAC;IAEX;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,IAAI,EAAE,2BAA2B,CAAC,IAAI,CAAC;IAEvC;;OAEG;IACH,IAAI,EAAE,mBAAmB,CAAC;IAE1B;;OAEG;IACH,MAAM,CAAC,EAAE,OAAO,CAAC;CAClB;AAED,yBAAiB,2BAA2B,CAAC;IAC3C;;OAEG;IACH,UAAiB,IAAI;QACnB;;WAEG;QACH,EAAE,EAAE,MAAM,CAAC;KACZ;CACF;AAED;;GAEG;AACH,MAAM,WAAW,yBAAyB;IACxC;;OAEG;IACH,EAAE,EAAE,MAAM,CAAC;IAEX;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,IAAI,EAAE,yBAAyB,CAAC,IAAI,CAAC;IAErC;;OAEG;IACH,IAAI,EAAE,iBAAiB,CAAC;IAExB;;OAEG;IACH,MAAM,CAAC,EAAE,OAAO,CAAC;CAClB;AAED,yBAAiB,yBAAyB,CAAC;IACzC;;OAEG;IACH,UAAiB,IAAI;QACnB;;WAEG;QACH,EAAE,EAAE,MAAM,CAAC;KACZ;CACF;AAED;;GAEG;AACH,MAAM,WAAW,4BAA4B;IAC3C;;OAEG;IACH,EAAE,EAAE,MAAM,CAAC;IAEX;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,IAAI,EAAE,4BAA4B,CAAC,IAAI,CAAC;IAExC;;OAEG;IACH,IAAI,EAAE,oBAAoB,CAAC;IAE3B;;OAEG;IACH,MAAM,CAAC,EAAE,OAAO,CAAC;CAClB;AAED,yBAAiB,4BAA4B,CAAC;IAC5C;;OAEG;IACH,UAAiB,IAAI;QACnB;;WAEG;QACH,EAAE,EAAE,MAAM,CAAC;KACZ;CACF;AAED;;GAEG;AACH,MAAM,WAAW,kCAAkC;IACjD;;OAEG;IACH,EAAE,EAAE,MAAM,CAAC;IAEX;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,IAAI,EAAE,kCAAkC,CAAC,IAAI,CAAC;IAE9C;;OAEG;IACH,IAAI,EAAE,2BAA2B,CAAC;IAElC;;OAEG;IACH,MAAM,CAAC,EAAE,OAAO,CAAC;CAClB;AAED,yBAAiB,kCAAkC,CAAC;IAClD;;OAEG;IACH,UAAiB,IAAI;QACnB;;WAEG;QACH,EAAE,EAAE,MAAM,CAAC;KACZ;CACF;AAED;;GAEG;AACH,MAAM,WAAW,+BAA+B;IAC9C;;OAEG;IACH,EAAE,EAAE,MAAM,CAAC;IAEX;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,IAAI,EAAE,+BAA+B,CAAC,IAAI,CAAC;IAE3C;;OAEG;IACH,IAAI,EAAE,wBAAwB,CAAC;IAE/B;;OAEG;IACH,MAAM,CAAC,EAAE,OAAO,CAAC;CAClB;AAED,yBAAiB,+BAA+B,CAAC;IAC/C;;OAEG;IACH,UAAiB,IAAI;QACnB;;WAEG;QACH,EAAE,EAAE,MAAM,CAAC;KACZ;CACF;AAED;;GAEG;AACH,MAAM,WAAW,kCAAkC;IACjD;;OAEG;IACH,EAAE,EAAE,MAAM,CAAC;IAEX;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,IAAI,EAAE,kCAAkC,CAAC,IAAI,CAAC;IAE9C;;OAEG;IACH,IAAI,EAAE,2BAA2B,CAAC;IAElC;;OAEG;IACH,MAAM,CAAC,EAAE,OAAO,CAAC;CAClB;AAED,yBAAiB,kCAAkC,CAAC;IAClD;;OAEG;IACH,UAAiB,IAAI;QACnB;;WAEG;QACH,EAAE,EAAE,MAAM,CAAC;KACZ;CACF;AAED;;GAEG;AACH,MAAM,WAAW,6BAA6B;IAC5C;;OAEG;IACH,EAAE,EAAE,MAAM,CAAC;IAEX;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,IAAI,EAAE,6BAA6B,CAAC,IAAI,CAAC;IAEzC;;OAEG;IACH,IAAI,EAAE,oBAAoB,CAAC;IAE3B;;OAEG;IACH,MAAM,CAAC,EAAE,OAAO,CAAC;CAClB;AAED,yBAAiB,6BAA6B,CAAC;IAC7C;;OAEG;IACH,UAAiB,IAAI;QACnB;;WAEG;QACH,EAAE,EAAE,MAAM,CAAC;KACZ;CACF;AAED;;GAEG;AACH,MAAM,WAAW,6BAA6B;IAC5C;;OAEG;IACH,EAAE,EAAE,MAAM,CAAC;IAEX;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,IAAI,EAAE,6BAA6B,CAAC,IAAI,CAAC;IAEzC;;OAEG;IACH,IAAI,EAAE,oBAAoB,CAAC;IAE3B;;OAEG;IACH,MAAM,CAAC,EAAE,OAAO,CAAC;CAClB;AAED,yBAAiB,6BAA6B,CAAC;IAC7C;;OAEG;IACH,UAAiB,IAAI;QACnB;;WAEG;QACH,EAAE,EAAE,MAAM,CAAC;KACZ;CACF;AAED;;GAEG;AACH,MAAM,WAAW,0BAA0B;IACzC;;OAEG;IACH,EAAE,EAAE,MAAM,CAAC;IAEX;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,IAAI,EAAE,0BAA0B,CAAC,IAAI,CAAC;IAEtC;;OAEG;IACH,IAAI,EAAE,iBAAiB,CAAC;IAExB;;OAEG;IACH,MAAM,CAAC,EAAE,OAAO,CAAC;CAClB;AAED,yBAAiB,0BAA0B,CAAC;IAC1C;;OAEG;IACH,UAAiB,IAAI;QACnB;;WAEG;QACH,EAAE,EAAE,MAAM,CAAC;KACZ;CACF;AAED;;GAEG;AACH,MAAM,WAAW,8BAA8B;IAC7C;;OAEG;IACH,EAAE,EAAE,MAAM,CAAC;IAEX;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,IAAI,EAAE,8BAA8B,CAAC,IAAI,CAAC;IAE1C;;OAEG;IACH,IAAI,EAAE,qBAAqB,CAAC;IAE5B;;OAEG;IACH,MAAM,CAAC,EAAE,OAAO,CAAC;CAClB;AAED,yBAAiB,8BAA8B,CAAC;IAC9C;;OAEG;IACH,UAAiB,IAAI;QACnB;;WAEG;QACH,EAAE,EAAE,MAAM,CAAC;KACZ;CACF;AAED;;GAEG;AACH,MAAM,MAAM,kBAAkB,GAC1B,0BAA0B,GAC1B,0BAA0B,GAC1B,wBAAwB,GACxB,uBAAuB,GACvB,2BAA2B,GAC3B,yBAAyB,GACzB,4BAA4B,GAC5B,kCAAkC,GAClC,+BAA+B,GAC/B,kCAAkC,GAClC,6BAA6B,GAC7B,6BAA6B,GAC7B,0BAA0B,GAC1B,8BAA8B,CAAC;AAEnC,MAAM,CAAC,OAAO,WAAW,QAAQ,CAAC;IAChC,OAAO,EACL,KAAK,0BAA0B,IAAI,0BAA0B,EAC7D,KAAK,0BAA0B,IAAI,0BAA0B,EAC7D,KAAK,wBAAwB,IAAI,wBAAwB,EACzD,KAAK,uBAAuB,IAAI,uBAAuB,EACvD,KAAK,2BAA2B,IAAI,2BAA2B,EAC/D,KAAK,yBAAyB,IAAI,yBAAyB,EAC3D,KAAK,4BAA4B,IAAI,4BAA4B,EACjE,KAAK,kCAAkC,IAAI,kCAAkC,EAC7E,KAAK,+BAA+B,IAAI,+BAA+B,EACvE,KAAK,kCAAkC,IAAI,kCAAkC,EAC7E,KAAK,6BAA6B,IAAI,6BAA6B,EACnE,KAAK,6BAA6B,IAAI,6BAA6B,EACnE,KAAK,0BAA0B,IAAI,0BAA0B,EAC7D,KAAK,8BAA8B,IAAI,8BAA8B,EACrE,KAAK,kBAAkB,IAAI,kBAAkB,GAC9C,CAAC;CACH"}