#!/usr/bin/env node

const http = require('http');

// Конфигурация
const API_BASE = 'http://localhost:8765';

// Функция для выполнения HTTP запросов
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          const result = {
            status: res.statusCode,
            headers: res.headers,
            data: res.headers['content-type']?.includes('application/json') 
              ? JSON.parse(body) 
              : body
          };
          resolve(result);
        } catch (error) {
          reject(error);
        }
      });
    });
    
    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

// Тест получения списка моделей
async function testModels() {
  console.log('🔍 Тестируем /v1/models...');
  
  try {
    const response = await makeRequest({
      hostname: 'localhost',
      port: 8765,
      path: '/v1/models',
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Статус:', response.status);
    console.log('📋 Модели:', response.data);
    return true;
  } catch (error) {
    console.error('❌ Ошибка при получении моделей:', error.message);
    return false;
  }
}

// Тест chat completions
async function testChatCompletions() {
  console.log('\n💬 Тестируем /v1/chat/completions...');
  
  const requestData = {
    model: 'gemini-2.5-flash',
    messages: [
      {
        role: 'user',
        content: 'Привет! Ответь кратко: как дела?'
      }
    ],
    max_tokens: 100
  };
  
  try {
    const response = await makeRequest({
      hostname: 'localhost',
      port: 8765,
      path: '/v1/chat/completions',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer dummy-key'
      }
    }, requestData);
    
    console.log('✅ Статус:', response.status);
    
    if (response.data && response.data.choices) {
      console.log('💭 Ответ:', response.data.choices[0]?.message?.content);
    } else {
      console.log('📄 Полный ответ:', response.data);
    }
    
    return true;
  } catch (error) {
    console.error('❌ Ошибка при отправке сообщения:', error.message);
    return false;
  }
}

// Проверка доступности сервера
async function checkServerHealth() {
  console.log('🏥 Проверяем доступность сервера...');
  
  try {
    const response = await makeRequest({
      hostname: 'localhost',
      port: 8765,
      path: '/',
      method: 'GET'
    });
    
    console.log('✅ Сервер доступен, статус:', response.status);
    return true;
  } catch (error) {
    console.error('❌ Сервер недоступен:', error.message);
    console.log('💡 Убедитесь, что сервер запущен: npm start');
    return false;
  }
}

// Основная функция тестирования
async function runTests() {
  console.log('🧪 Запуск тестов API...\n');
  
  // Проверяем доступность сервера
  const serverOk = await checkServerHealth();
  if (!serverOk) {
    process.exit(1);
  }
  
  console.log('');
  
  // Тестируем endpoints
  const modelsOk = await testModels();
  const chatOk = await testChatCompletions();
  
  console.log('\n📊 Результаты тестов:');
  console.log(`Models API: ${modelsOk ? '✅' : '❌'}`);
  console.log(`Chat API: ${chatOk ? '✅' : '❌'}`);
  
  if (modelsOk && chatOk) {
    console.log('\n🎉 Все тесты прошли успешно!');
    process.exit(0);
  } else {
    console.log('\n💥 Некоторые тесты не прошли');
    process.exit(1);
  }
}

// Запуск тестов
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { testModels, testChatCompletions, checkServerHealth };
