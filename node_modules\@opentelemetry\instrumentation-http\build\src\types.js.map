{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/types.ts"], "names": [], "mappings": "", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { Span, SpanAttributes } from '@opentelemetry/api';\nimport type * as http from 'http';\nimport type * as https from 'https';\nimport {\n  ClientRequest,\n  get,\n  IncomingMessage,\n  request,\n  ServerResponse,\n  RequestOptions,\n} from 'http';\nimport * as url from 'url';\nimport { InstrumentationConfig } from '@opentelemetry/instrumentation';\n\nexport type IgnoreMatcher = string | RegExp | ((url: string) => boolean);\nexport type HttpCallback = (res: IncomingMessage) => void;\nexport type RequestFunction = typeof request;\nexport type GetFunction = typeof get;\n\nexport type HttpCallbackOptional = HttpCallback | undefined;\n\n// from node 10+\nexport type RequestSignature = [http.RequestOptions, HttpCallbackOptional] &\n  HttpCallback;\n\nexport type HttpRequestArgs = Array<HttpCallbackOptional | RequestSignature>;\n\nexport type ParsedRequestOptions =\n  | (http.RequestOptions & Partial<url.UrlWithParsedQuery>)\n  | http.RequestOptions;\nexport type Http = typeof http;\nexport type Https = typeof https;\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport type Func<T> = (...args: any[]) => T;\n\nexport interface HttpCustomAttributeFunction {\n  (\n    span: Span,\n    request: ClientRequest | IncomingMessage,\n    response: IncomingMessage | ServerResponse\n  ): void;\n}\n\nexport interface IgnoreIncomingRequestFunction {\n  (request: IncomingMessage): boolean;\n}\n\nexport interface IgnoreOutgoingRequestFunction {\n  (request: RequestOptions): boolean;\n}\n\nexport interface HttpRequestCustomAttributeFunction {\n  (span: Span, request: ClientRequest | IncomingMessage): void;\n}\n\nexport interface HttpResponseCustomAttributeFunction {\n  (span: Span, response: IncomingMessage | ServerResponse): void;\n}\n\nexport interface StartIncomingSpanCustomAttributeFunction {\n  (request: IncomingMessage): SpanAttributes;\n}\n\nexport interface StartOutgoingSpanCustomAttributeFunction {\n  (request: RequestOptions): SpanAttributes;\n}\n\n/**\n * Options available for the HTTP instrumentation (see [documentation](https://github.com/open-telemetry/opentelemetry-js/tree/main/packages/opentelemetry-instrumentation-http#http-instrumentation-options))\n */\nexport interface HttpInstrumentationConfig extends InstrumentationConfig {\n  /**\n   * Not trace all incoming requests that match paths\n   * @deprecated use `ignoreIncomingRequestHook` instead\n   */\n  ignoreIncomingPaths?: IgnoreMatcher[];\n  /** Not trace all incoming requests that matched with custom function */\n  ignoreIncomingRequestHook?: IgnoreIncomingRequestFunction;\n  /**\n   * Not trace all outgoing requests that match urls\n   * @deprecated use `ignoreOutgoingRequestHook` instead\n   */\n  ignoreOutgoingUrls?: IgnoreMatcher[];\n  /** Not trace all outgoing requests that matched with custom function */\n  ignoreOutgoingRequestHook?: IgnoreOutgoingRequestFunction;\n  /** Function for adding custom attributes after response is handled */\n  applyCustomAttributesOnSpan?: HttpCustomAttributeFunction;\n  /** Function for adding custom attributes before request is handled */\n  requestHook?: HttpRequestCustomAttributeFunction;\n  /** Function for adding custom attributes before response is handled */\n  responseHook?: HttpResponseCustomAttributeFunction;\n  /** Function for adding custom attributes before a span is started in incomingRequest */\n  startIncomingSpanHook?: StartIncomingSpanCustomAttributeFunction;\n  /** Function for adding custom attributes before a span is started in outgoingRequest */\n  startOutgoingSpanHook?: StartOutgoingSpanCustomAttributeFunction;\n  /** The primary server name of the matched virtual host. */\n  serverName?: string;\n  /** Require parent to create span for outgoing requests */\n  requireParentforOutgoingSpans?: boolean;\n  /** Require parent to create span for incoming requests */\n  requireParentforIncomingSpans?: boolean;\n  /** Map the following HTTP headers to span attributes. */\n  headersToSpanAttributes?: {\n    client?: { requestHeaders?: string[]; responseHeaders?: string[] };\n    server?: { requestHeaders?: string[]; responseHeaders?: string[] };\n  };\n}\n\nexport interface Err extends Error {\n  errno?: number;\n  code?: string;\n  path?: string;\n  syscall?: string;\n  stack?: string;\n}\n"]}