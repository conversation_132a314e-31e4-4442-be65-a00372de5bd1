{"version": 3, "file": "OTLPExporterBrowserBase.js", "sourceRoot": "", "sources": ["../../../../src/platform/browser/OTLPExporterBrowserBase.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,OAAO,EAAE,gBAAgB,EAAE,MAAM,wBAAwB,CAAC;AAG1D,OAAO,EAAE,YAAY,EAAE,MAAM,YAAY,CAAC;AAC1C,OAAO,EAAE,cAAc,EAAE,WAAW,EAAE,MAAM,QAAQ,CAAC;AACrD,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAC1C,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,qBAAqB,CAAC;AAG3D;;GAEG;AACH;IAGU,2CAAoD;IAM5D;;;;OAIG;IACH,iCACE,MAAmC,EACnC,UAAsD,EACtD,WAAmB;QAFnB,uBAAA,EAAA,WAAmC;QADrC,YAKE,kBAAM,MAAM,CAAC,SAgBd;QA9BO,aAAO,GAAY,KAAK,CAAC;QAe/B,KAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,KAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAChC,KAAI,CAAC,OAAO;YACV,CAAC,CAAC,MAAM,CAAC,OAAO,IAAI,OAAO,SAAS,CAAC,UAAU,KAAK,UAAU,CAAC;QACjE,IAAI,KAAI,CAAC,OAAO,EAAE;YAChB,KAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,MAAM,CAC3B,EAAE,EACF,YAAY,CAAC,MAAM,CAAC,OAAO,CAAC,EAC5B,YAAY,CAAC,uBAAuB,CAClC,MAAM,EAAE,CAAC,0BAA0B,CACpC,CACF,CAAC;SACH;aAAM;YACL,KAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;SACpB;;IACH,CAAC;IAED,wCAAM,GAAN,cAAgB,CAAC;IAEjB,4CAAU,GAAV,cAAoB,CAAC;IAErB,sCAAI,GAAJ,UACE,KAAmB,EACnB,SAAqB,EACrB,OAAqD;QAHvD,iBAyCC;;QApCC,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;YAC/B,IAAI,CAAC,KAAK,CAAC,+CAA+C,CAAC,CAAC;YAC5D,OAAO;SACR;QACD,IAAM,IAAI,GAAG,MAAA,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,KAAK,CAAC,mCAAI,IAAI,UAAU,EAAE,CAAC;QAE1E,IAAM,OAAO,GAAG,IAAI,OAAO,CAAO,UAAC,OAAO,EAAE,MAAM;YAChD,IAAI,KAAI,CAAC,OAAO,EAAE;gBAChB,WAAW,CACT,IAAI,EACJ,KAAI,CAAC,GAAG,wBAEH,KAAI,CAAC,QAAQ,KAChB,cAAc,EAAE,KAAI,CAAC,YAAY,KAEnC,KAAI,CAAC,aAAa,EAClB,OAAO,EACP,MAAM,CACP,CAAC;aACH;iBAAM;gBACL,cAAc,CACZ,IAAI,EACJ,KAAI,CAAC,GAAG,EACR,EAAE,IAAI,EAAE,KAAI,CAAC,YAAY,EAAE,EAC3B,OAAO,EACP,MAAM,CACP,CAAC;aACH;QACH,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAE5B,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACpC,IAAM,UAAU,GAAG;YACjB,IAAM,KAAK,GAAG,KAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YACrD,KAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC;QACF,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;IACvC,CAAC;IACH,8BAAC;AAAD,CAAC,AAnFD,CAGU,gBAAgB,GAgFzB", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { OTLPExporterBase } from '../../OTLPExporterBase';\nimport { OTLPExporterConfigBase } from '../../types';\nimport * as otlpTypes from '../../types';\nimport { parseHeaders } from '../../util';\nimport { sendWithBeacon, sendWithXhr } from './util';\nimport { diag } from '@opentelemetry/api';\nimport { getEnv, baggageUtils } from '@opentelemetry/core';\nimport { ISerializer } from '@opentelemetry/otlp-transformer';\n\n/**\n * Collector Metric Exporter abstract base class\n */\nexport abstract class OTLPExporterBrowserBase<\n  ExportItem,\n  ServiceResponse,\n> extends OTLPExporterBase<OTLPExporterConfigBase, ExportItem> {\n  protected _headers: Record<string, string>;\n  private _useXHR: boolean = false;\n  private _contentType: string;\n  private _serializer: ISerializer<ExportItem[], ServiceResponse>;\n\n  /**\n   * @param config\n   * @param serializer\n   * @param contentType\n   */\n  constructor(\n    config: OTLPExporterConfigBase = {},\n    serializer: ISerializer<ExportItem[], ServiceResponse>,\n    contentType: string\n  ) {\n    super(config);\n    this._serializer = serializer;\n    this._contentType = contentType;\n    this._useXHR =\n      !!config.headers || typeof navigator.sendBeacon !== 'function';\n    if (this._useXHR) {\n      this._headers = Object.assign(\n        {},\n        parseHeaders(config.headers),\n        baggageUtils.parseKeyPairsIntoRecord(\n          getEnv().OTEL_EXPORTER_OTLP_HEADERS\n        )\n      );\n    } else {\n      this._headers = {};\n    }\n  }\n\n  onInit(): void {}\n\n  onShutdown(): void {}\n\n  send(\n    items: ExportItem[],\n    onSuccess: () => void,\n    onError: (error: otlpTypes.OTLPExporterError) => void\n  ): void {\n    if (this._shutdownOnce.isCalled) {\n      diag.debug('Shutdown already started. Cannot send objects');\n      return;\n    }\n    const body = this._serializer.serializeRequest(items) ?? new Uint8Array();\n\n    const promise = new Promise<void>((resolve, reject) => {\n      if (this._useXHR) {\n        sendWithXhr(\n          body,\n          this.url,\n          {\n            ...this._headers,\n            'Content-Type': this._contentType,\n          },\n          this.timeoutMillis,\n          resolve,\n          reject\n        );\n      } else {\n        sendWithBeacon(\n          body,\n          this.url,\n          { type: this._contentType },\n          resolve,\n          reject\n        );\n      }\n    }).then(onSuccess, onError);\n\n    this._sendingPromises.push(promise);\n    const popPromise = () => {\n      const index = this._sendingPromises.indexOf(promise);\n      this._sendingPromises.splice(index, 1);\n    };\n    promise.then(popPromise, popPromise);\n  }\n}\n"]}