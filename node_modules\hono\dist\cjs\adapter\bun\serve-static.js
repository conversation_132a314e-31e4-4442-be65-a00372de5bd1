"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var serve_static_exports = {};
__export(serve_static_exports, {
  serveStatic: () => serveStatic
});
module.exports = __toCommonJS(serve_static_exports);
var import_promises = require("node:fs/promises");
var import_serve_static = require("../../middleware/serve-static");
const serveStatic = (options) => {
  return async function serveStatic2(c, next) {
    const getContent = async (path) => {
      path = path.startsWith("/") ? path : `./${path}`;
      const file = Bun.file(path);
      return await file.exists() ? file : null;
    };
    const pathResolve = (path) => {
      return path.startsWith("/") ? path : `./${path}`;
    };
    const isDir = async (path) => {
      let isDir2;
      try {
        const stats = await (0, import_promises.stat)(path);
        isDir2 = stats.isDirectory();
      } catch {
      }
      return isDir2;
    };
    return (0, import_serve_static.serveStatic)({
      ...options,
      getContent,
      pathResolve,
      isDir
    })(c, next);
  };
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  serveStatic
});
