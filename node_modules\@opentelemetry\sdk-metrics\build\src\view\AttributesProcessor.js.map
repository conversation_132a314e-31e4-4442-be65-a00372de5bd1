{"version": 3, "file": "AttributesProcessor.js", "sourceRoot": "", "sources": ["../../../src/view/AttributesProcessor.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAIH;;;;GAIG;AACH,MAAsB,mBAAmB;IAavC,MAAM,CAAC,IAAI;QACT,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AAhBD,kDAgBC;AAED,MAAa,uBAAwB,SAAQ,mBAAmB;IAC9D,OAAO,CAAC,QAA0B,EAAE,QAAkB;QACpD,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF;AAJD,0DAIC;AAED;;;GAGG;AACH,MAAa,4BAA6B,SAAQ,mBAAmB;IACnE,YAAoB,sBAAgC;QAClD,KAAK,EAAE,CAAC;QADU,2BAAsB,GAAtB,sBAAsB,CAAU;IAEpD,CAAC;IAED,OAAO,CAAC,QAA0B,EAAE,QAAiB;QACnD,MAAM,kBAAkB,GAAqB,EAAE,CAAC;QAChD,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;aAClB,MAAM,CAAC,aAAa,CAAC,EAAE,CACtB,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,aAAa,CAAC,CACpD;aACA,OAAO,CACN,aAAa,CAAC,EAAE,CACd,CAAC,kBAAkB,CAAC,aAAa,CAAC,GAAG,QAAQ,CAAC,aAAa,CAAC,CAAC,CAChE,CAAC;QACJ,OAAO,kBAAkB,CAAC;IAC5B,CAAC;CACF;AAjBD,oEAiBC;AAED,MAAM,IAAI,GAAG,IAAI,uBAAuB,EAAE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context, MetricAttributes } from '@opentelemetry/api';\n\n/**\n * The {@link AttributesProcessor} is responsible for customizing which\n * attribute(s) are to be reported as metrics dimension(s) and adding\n * additional dimension(s) from the {@link Context}.\n */\nexport abstract class AttributesProcessor {\n  /**\n   * Process the metric instrument attributes.\n   *\n   * @param incoming The metric instrument attributes.\n   * @param context The active context when the instrument is synchronous.\n   * `undefined` otherwise.\n   */\n  abstract process(\n    incoming: MetricAttributes,\n    context?: Context\n  ): MetricAttributes;\n\n  static Noop() {\n    return NOOP;\n  }\n}\n\nexport class NoopAttributesProcessor extends AttributesProcessor {\n  process(incoming: MetricAttributes, _context?: Context) {\n    return incoming;\n  }\n}\n\n/**\n * {@link AttributesProcessor} that filters by allowed attribute names and drops any names that are not in the\n * allow list.\n */\nexport class FilteringAttributesProcessor extends AttributesProcessor {\n  constructor(private _allowedAttributeNames: string[]) {\n    super();\n  }\n\n  process(incoming: MetricAttributes, _context: Context): MetricAttributes {\n    const filteredAttributes: MetricAttributes = {};\n    Object.keys(incoming)\n      .filter(attributeName =>\n        this._allowedAttributeNames.includes(attributeName)\n      )\n      .forEach(\n        attributeName =>\n          (filteredAttributes[attributeName] = incoming[attributeName])\n      );\n    return filteredAttributes;\n  }\n}\n\nconst NOOP = new NoopAttributesProcessor();\n"]}