{"name": "@opentelemetry/otlp-grpc-exporter-base", "version": "0.52.1", "description": "OpenTelemetry OTLP-gRPC Exporter base (for internal use only)", "main": "build/src/index.js", "types": "build/src/index.d.ts", "repository": "open-telemetry/opentelemetry-js", "scripts": {"prepublishOnly": "npm run compile", "codecov": "nyc report --reporter=json && codecov -f coverage/*.json -p ../../../", "compile": "tsc --build", "clean": "tsc --build --clean", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "tdd": "npm run test -- --watch-extensions ts --watch", "test": "nyc ts-mocha -p tsconfig.json 'test/**/*.test.ts'", "version": "node ../../../scripts/version-update.js", "watch": "tsc -w", "precompile": "cross-var lerna run version --scope $npm_package_name --include-dependencies", "prewatch": "npm run precompile", "align-api-deps": "node ../../../scripts/align-api-deps.js", "maint:regenerate-test-certs": "cd test/certs && ./regenerate.sh"}, "keywords": ["opentelemetry", "nodejs", "grpc", "tracing", "profiling", "metrics", "stats"], "author": "OpenTelemetry Authors", "license": "Apache-2.0", "engines": {"node": ">=14"}, "files": ["build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts", "doc", "LICENSE", "README.md"], "publishConfig": {"access": "public"}, "devDependencies": {"@opentelemetry/api": "1.9.0", "@opentelemetry/resources": "1.25.1", "@opentelemetry/sdk-trace-base": "1.25.1", "@types/mocha": "10.0.6", "@types/node": "18.6.5", "@types/sinon": "17.0.3", "codecov": "3.8.3", "cross-var": "1.1.0", "lerna": "6.6.2", "mocha": "10.2.0", "nyc": "15.1.0", "sinon": "15.1.2", "ts-loader": "9.5.1", "ts-mocha": "10.0.0", "typescript": "4.4.4"}, "peerDependencies": {"@opentelemetry/api": "^1.0.0"}, "dependencies": {"@grpc/grpc-js": "^1.7.1", "@opentelemetry/core": "1.25.1", "@opentelemetry/otlp-exporter-base": "0.52.1", "@opentelemetry/otlp-transformer": "0.52.1"}, "homepage": "https://github.com/open-telemetry/opentelemetry-js/tree/main/experimental/packages/otlp-grpc-exporter-base", "sideEffects": false, "gitHead": "0608f405573901e54db01e44c533009cf28be262"}