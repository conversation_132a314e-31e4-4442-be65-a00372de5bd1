{"name": "@opentelemetry/exporter-metrics-otlp-grpc", "version": "0.52.1", "description": "OpenTelemetry Collector Metrics Exporter allows user to send collected metrics to the OpenTelemetry Collector", "main": "build/src/index.js", "types": "build/src/index.d.ts", "repository": "open-telemetry/opentelemetry-js", "scripts": {"prepublishOnly": "npm run compile", "compile": "tsc --build", "clean": "tsc --build --clean", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "tdd": "npm run test -- --watch-extensions ts --watch", "test": "nyc ts-mocha -p tsconfig.json 'test/**/*.test.ts'", "version": "node ../../../scripts/version-update.js", "watch": "tsc -w", "precompile": "cross-var lerna run version --scope $npm_package_name --include-dependencies", "prewatch": "npm run precompile", "peer-api-check": "node ../../../scripts/peer-api-check.js", "codecov": "nyc report --reporter=json && codecov -f coverage/*.json -p ../../../", "align-api-deps": "node ../../../scripts/align-api-deps.js", "maint:regenerate-test-certs": "cd test/certs && ./regenerate.sh"}, "keywords": ["opentelemetry", "nodejs", "grpc", "tracing", "profiling", "metrics", "stats"], "author": "OpenTelemetry Authors", "license": "Apache-2.0", "engines": {"node": ">=14"}, "files": ["build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts", "build/protos/**/*.proto", "doc", "LICENSE", "README.md"], "publishConfig": {"access": "public"}, "devDependencies": {"@grpc/proto-loader": "^0.7.10", "@opentelemetry/api": "1.9.0", "@types/mocha": "10.0.6", "@types/node": "18.6.5", "@types/sinon": "17.0.3", "codecov": "3.8.3", "cross-var": "1.1.0", "lerna": "6.6.2", "mocha": "10.2.0", "nyc": "15.1.0", "sinon": "15.1.2", "ts-loader": "9.5.1", "ts-mocha": "10.0.0", "typescript": "4.4.4"}, "peerDependencies": {"@opentelemetry/api": "^1.3.0"}, "dependencies": {"@grpc/grpc-js": "^1.7.1", "@opentelemetry/core": "1.25.1", "@opentelemetry/exporter-metrics-otlp-http": "0.52.1", "@opentelemetry/otlp-exporter-base": "0.52.1", "@opentelemetry/otlp-grpc-exporter-base": "0.52.1", "@opentelemetry/otlp-transformer": "0.52.1", "@opentelemetry/resources": "1.25.1", "@opentelemetry/sdk-metrics": "1.25.1"}, "homepage": "https://github.com/open-telemetry/opentelemetry-js/tree/main/experimental/packages/opentelemetry-exporter-metrics-otlp-grpc", "sideEffects": false, "gitHead": "0608f405573901e54db01e44c533009cf28be262"}