# ============================================================================
# Gemini CLI OpenAI Proxy Server Launcher
# ============================================================================
# Этот скрипт запускает локальный OpenAI-совместимый API сервер для Gemini
# Используйте его для интеграции с Cursor, Cline, Rucode и другими IDE
# ============================================================================

param(
    [switch]$UpdateCredentials,  # Обновить OAuth credentials
    [switch]$Test,              # Запустить тесты после старта
    [switch]$Help               # Показать справку
)

function Write-ColorText {
    param([string]$Text, [string]$Color = "White")
    $Colors = @{
        Success = "Green"
        Warning = "Yellow" 
        Error = "Red"
        Info = "Cyan"
        Header = "Magenta"
    }
    Write-Host $Text -ForegroundColor $Colors[$Color]
}

function Show-Header {
    Clear-Host
    Write-ColorText "================================================================================" "Header"
    Write-ColorText "                    GEMINI CLI OPENAI PROXY SERVER                             " "Header"
    Write-ColorText "                                                                               " "Header"
    Write-ColorText "  Превращаем Gemini в OpenAI-совместимый API для использования в любых IDE    " "Header"
    Write-ColorText "================================================================================" "Header"
    Write-Host ""
}

function Show-Help {
    Show-Header
    Write-ColorText "СПРАВКА ПО ИСПОЛЬЗОВАНИЮ:" "Info"
    Write-Host ""
    Write-ColorText "Параметры запуска:" "Info"
    Write-Host "  -UpdateCredentials    Обновить OAuth credentials из Gemini CLI"
    Write-Host "  -Test                 Запустить тесты API после старта сервера"
    Write-Host "  -Help                 Показать эту справку"
    Write-Host ""
    Write-ColorText "Примеры:" "Info"
    Write-Host "  .\start-proxy.ps1                    # Обычный запуск"
    Write-Host "  .\start-proxy.ps1 -UpdateCredentials # Обновить токены и запустить"
    Write-Host "  .\start-proxy.ps1 -Test              # Запустить с тестами"
    Write-Host ""
    Write-ColorText "Настройка в IDE:" "Info"
    Write-Host "  Base URL:    http://127.0.0.1:8787/v1"
    Write-Host "  API Key:     sk-gemini-proxy-12345"
    Write-Host "  Models:      gemini-2.5-pro, gemini-2.5-flash"
    Write-Host ""
    exit 0
}

function Test-Prerequisites {
    Write-ColorText "Проверяем предварительные требования..." "Info"
    
    # Проверяем Node.js
    try {
        $nodeVersion = node --version 2>$null
        Write-ColorText "Node.js: $nodeVersion" "Success"
    } catch {
        Write-ColorText "Node.js не найден! Установите Node.js с https://nodejs.org" "Error"
        exit 1
    }
    
    # Проверяем npm
    try {
        $npmVersion = npm --version 2>$null
        Write-ColorText "npm: v$npmVersion" "Success"
    } catch {
        Write-ColorText "npm не найден!" "Error"
        exit 1
    }
    
    # Проверяем Gemini CLI
    try {
        $geminiVersion = gemini --version 2>$null
        Write-ColorText "Gemini CLI установлен" "Success"
    } catch {
        Write-ColorText "Gemini CLI не найден. Устанавливаем..." "Warning"
        npm install -g @google/gemini-cli
        Write-ColorText "Gemini CLI установлен" "Success"
    }
    
    # Проверяем файлы проекта
    if (-not (Test-Path "package.json")) {
        Write-ColorText "package.json не найден! Запустите скрипт из папки проекта." "Error"
        exit 1
    }
    
    if (-not (Test-Path "src/index.ts")) {
        Write-ColorText "Исходные файлы не найдены! Проверьте структуру проекта." "Error"
        exit 1
    }
    
    Write-ColorText "Все предварительные требования выполнены" "Success"
    Write-Host ""
}

function Update-OAuthCredentials {
    Write-ColorText "Обновляем OAuth credentials..." "Info"
    
    $credentialsPath = "$env:USERPROFILE\.gemini\oauth_creds.json"
    
    if (-not (Test-Path $credentialsPath)) {
        Write-ColorText "OAuth credentials не найдены!" "Error"
        Write-ColorText "Запустите 'gemini' и выполните аутентификацию с Google" "Warning"
        Write-Host ""
        Write-ColorText "Запускаем Gemini CLI для аутентификации..." "Info"
        Start-Process "gemini" -Wait
        
        if (-not (Test-Path $credentialsPath)) {
            Write-ColorText "Аутентификация не завершена!" "Error"
            exit 1
        }
    }
    
    try {
        $credentials = Get-Content $credentialsPath | ConvertFrom-Json | ConvertTo-Json -Compress
        
        # Читаем текущий .dev.vars
        $devVarsContent = Get-Content ".dev.vars" -Raw
        
        # Обновляем GCP_SERVICE_ACCOUNT
        $devVarsContent = $devVarsContent -replace 'GCP_SERVICE_ACCOUNT=.*', "GCP_SERVICE_ACCOUNT=$credentials"
        
        # Записываем обратно
        $devVarsContent | Set-Content ".dev.vars" -NoNewline
        
        Write-ColorText "OAuth credentials обновлены" "Success"
    } catch {
        Write-ColorText "Ошибка обновления credentials: $($_.Exception.Message)" "Error"
        exit 1
    }
    
    Write-Host ""
}

function Install-Dependencies {
    Write-ColorText "Проверяем зависимости..." "Info"
    
    if (-not (Test-Path "node_modules")) {
        Write-ColorText "Устанавливаем зависимости..." "Info"
        npm install
        if ($LASTEXITCODE -ne 0) {
            Write-ColorText "Ошибка установки зависимостей!" "Error"
            exit 1
        }
        Write-ColorText "Зависимости установлены" "Success"
    } else {
        Write-ColorText "Зависимости уже установлены" "Success"
    }
    Write-Host ""
}

function Show-Configuration {
    Write-ColorText "КОНФИГУРАЦИЯ СЕРВЕРА:" "Info"
    Write-Host ""
    Write-ColorText "Локальный URL:      http://127.0.0.1:8787" "Success"
    Write-ColorText "API Base URL:       http://127.0.0.1:8787/v1" "Success"
    Write-ColorText "API Key:            sk-gemini-proxy-12345" "Success"
    Write-Host ""
    Write-ColorText "Доступные модели:" "Info"
    Write-Host "   • gemini-2.5-pro    (для сложных задач)"
    Write-Host "   • gemini-2.5-flash  (для быстрых ответов)"
    Write-Host ""
    Write-ColorText "НАСТРОЙКА В IDE:" "Info"
    Write-Host ""
    Write-ColorText "Cursor:" "Info"
    Write-Host "   Settings → Models → Add Model"
    Write-Host "   Provider: OpenAI"
    Write-Host "   Base URL: http://127.0.0.1:8787/v1"
    Write-Host "   API Key: sk-gemini-proxy-12345"
    Write-Host ""
    Write-ColorText "Cline (VS Code):" "Info"
    Write-Host "   API Provider: OpenAI"
    Write-Host "   Base URL: http://127.0.0.1:8787/v1"
    Write-Host "   API Key: sk-gemini-proxy-12345"
    Write-Host ""
    Write-ColorText "Rucode:" "Info"
    Write-Host "   Settings → AI → Custom OpenAI"
    Write-Host "   Endpoint: http://127.0.0.1:8787/v1"
    Write-Host "   API Key: sk-gemini-proxy-12345"
    Write-Host ""
    Write-ColorText "ВАЖНО: Сервер должен быть запущен для работы API!" "Warning"
    Write-Host ""
}

function Start-Server {
    Write-ColorText "Запускаем Gemini OpenAI Proxy Server..." "Info"
    Write-Host ""
    
    # Показываем информацию о запуске
    Write-ColorText "Сервер будет доступен по адресу: http://127.0.0.1:8787" "Success"
    Write-ColorText "API endpoint: http://127.0.0.1:8787/v1" "Success"
    Write-ColorText "API Key: sk-gemini-proxy-12345" "Success"
    Write-Host ""
    Write-ColorText "Запуск может занять несколько секунд..." "Info"
    Write-Host ""
    Write-ColorText "Для остановки сервера нажмите Ctrl+C" "Warning"
    Write-Host ""
    Write-ColorText "================================================================================" "Header"
    Write-Host ""
    
    # Запускаем сервер
    try {
        npm run dev
    } catch {
        Write-ColorText "Ошибка запуска сервера: $($_.Exception.Message)" "Error"
        exit 1
    }
}

function Test-API {
    Write-ColorText "Запускаем тесты API..." "Info"
    Write-Host ""
    
    # Ждем немного, чтобы сервер запустился
    Start-Sleep -Seconds 3
    
    try {
        node test-proxy.js
        Write-ColorText "Тесты завершены" "Success"
    } catch {
        Write-ColorText "Ошибка тестирования: $($_.Exception.Message)" "Error"
    }
    Write-Host ""
}

function Show-QuickStart {
    Write-Host ""
    Write-ColorText "БЫСТРЫЙ СТАРТ:" "Header"
    Write-Host ""
    Write-ColorText "1. Скопируйте эти настройки в ваше IDE:" "Info"
    Write-Host ""
    Write-Host "   Base URL: " -NoNewline
    Write-ColorText "http://127.0.0.1:8787/v1" "Success"
    Write-Host "   API Key:  " -NoNewline  
    Write-ColorText "sk-gemini-proxy-12345" "Success"
    Write-Host ""
    Write-ColorText "2. Выберите модель:" "Info"
    Write-Host "   • gemini-2.5-pro (медленнее, но умнее)"
    Write-Host "   • gemini-2.5-flash (быстрее)"
    Write-Host ""
    Write-ColorText "3. Начните использовать Gemini через OpenAI API!" "Success"
    Write-Host ""
}

# ============================================================================
# ОСНОВНАЯ ЛОГИКА СКРИПТА
# ============================================================================

# Показать справку если запрошена
if ($Help) {
    Show-Help
}

# Показать заголовок
Show-Header

# Проверить предварительные требования
Test-Prerequisites

# Обновить credentials если запрошено
if ($UpdateCredentials) {
    Update-OAuthCredentials
}

# Установить зависимости
Install-Dependencies

# Показать конфигурацию
Show-Configuration

# Показать быстрый старт
Show-QuickStart

# Запустить тесты в фоне если запрошено
if ($Test) {
    Write-ColorText "Тесты будут запущены после старта сервера..." "Info"
    Write-Host ""
    
    # Запускаем тесты в фоне через несколько секунд
    Start-Job -ScriptBlock {
        Start-Sleep -Seconds 5
        Set-Location $using:PWD
        node test-proxy.js
    } | Out-Null
}

# Запустить сервер (блокирующий вызов)
Start-Server
